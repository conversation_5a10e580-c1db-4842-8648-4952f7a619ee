import {Box} from '@mui/material';
import React from 'react';
import repfabricLogoPng from './repfabric-logo-primary.png';

interface RepfabricLogoProps {
  sx?: object;
  alt?: string;
}

const RepfabricLogo: React.FC<RepfabricLogoProps> = ({sx = {}, alt = 'REPFABRIC Logo'}) => {
  return (
    <Box
      sx={{
        display: 'inline-block',
        ...sx,
      }}
    >
      <img
        src={repfabricLogoPng}
        alt={alt}
        style={{
          height: 'auto',
          maxWidth: '25%',
          maxHeight: '25%',
          width: 'auto',
          display: 'block',
          margin: '0 auto',
        }}
      />
    </Box>
  );
};

export default RepfabricLogo;
