import {CellContext} from '@tanstack/react-table';
import StatusChip from 'Components/StatusChip/StatusChip';
import {ActionButtons} from './PlanPage';

export const DEFAULT_LIMIT = 20;
export const DEFAULT_OFFSET = 0;

export enum PlanStatus {
  ACTIVE,
  INACTIVE,
}

const whiteMain = 'white.main';

export const getStatusColor = (status: PlanStatus): string => {
  const statusColorMap: Record<number, string> = {
    [PlanStatus.ACTIVE]: `alert.success.bg`,
    [PlanStatus.INACTIVE]: `alert.error.bg`,
  };
  return statusColorMap[status] || whiteMain;
};

export enum PlanTierType {
  PREMIUM = 'premium',
  STANDARD = 'standard',
}

export const groupedFeatureOptions = [
  {
    label: 'Premium',
    value: PlanTierType.PREMIUM,
    features: ['Silo compute', 'Silo storage'],
  },
  {
    label: 'Standard',
    value: PlanTierType.STANDARD,
    features: ['Poled compute', 'Silo storage'],
  },
];

export const getFontColor = (status: PlanStatus): string => {
  const statusColorMap: Record<PlanStatus, string> = {
    [PlanStatus.ACTIVE]: `alert.success.onBg`,
    [PlanStatus.INACTIVE]: `alert.error.onBg`,
  };
  return statusColorMap[status] || whiteMain;
};

export const getIndicatorColor = (status: PlanStatus): string => {
  const statusColorMap: Record<PlanStatus, string> = {
    [PlanStatus.ACTIVE]: 'alert.success.main',
    [PlanStatus.INACTIVE]: 'alert.error.main',
  };
  return statusColorMap[status] || whiteMain;
};

export const getStatusLabel = (status: PlanStatus | number): string => {
  const statusLabelMap: Record<PlanStatus | number, string> = {
    [PlanStatus.ACTIVE]: 'Active',
    [PlanStatus.INACTIVE]: 'Inactive',
  };
  return statusLabelMap[status] || '';
};

interface BillingCycle {
  deleted: boolean;
  deletedOn: string | null;
  deletedBy: string | null;
  createdOn: string;
  modifiedOn: string | null;
  createdBy: string;
  modifiedBy: string | null;
  id: string;
  cycleName: string;
  duration: number;
  durationUnit: string;
  description: string;
}

interface ICurrency {
  id: string;
  currencyCode: string;
  currencyName: string;
  symbol: string;
  country: string;
}

interface ConfigureDevice {
  deleted: boolean;
  deletedOn: string | null;
  deletedBy: string | null;
  createdOn: string;
  modifiedOn: string | null;
  createdBy: string;
  modifiedBy: string | null;
  id: string;
  min: string;
  max: string;
}

interface PlanTableRow {
  name: string;
  price: number;
  status: PlanStatus;
  configureDevice: ConfigureDevice;
  currency: ICurrency;
  billingCycle: BillingCycle;
  tier: string;
}

interface PlanTableColumn {
  header: string;
  accessorKey?: keyof PlanTableRow;
  id?: string;
  cell?: (context: CellContext<PlanTableRow, unknown>) => React.ReactNode;
}

const columnNameMap: Record<string, string> = {
  planName: 'name',
  status: 'status',
  configureDevice: 'configureDevice',
  currency: 'currency',
  billingCycle: 'billingCycle',
  tier: 'tier',
};

/**
 * Returns the corresponding backend column name for a given frontend column name.
 * If the column name does not exist in the mapping, returns the original column name.
 *
 * @param columnName - The frontend column name to map.
 * @returns The backend column name if found in the mapping; otherwise, the original column name.
 */
export const getBackendColumnName = (columnName: string): string => columnNameMap[columnName] || columnName;

/**
 * Capitalizes the first character of the given string and converts the rest to lowercase.
 *
 * @param str - The string to capitalize.
 * @returns The capitalized string.
 */
export const capitalize = (str: string): string => str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();

/**
 * Defines the column configuration for the Plans table.
 *
 * Each column object specifies how the column should be rendered, including:
 * - `header`: The display name of the column.
 * - `accessorKey`: The key to access the corresponding value from the row data.
 * - `id`: A unique identifier for the column.
 * - `cell`: (Optional) A custom cell renderer function for displaying complex or formatted data.
 *
 * Columns include:
 * - Plan name
 * - Status (with custom status chip rendering)
 * - Number of devices (range or placeholder)
 * - Subscription Tenure (formatted with capitalization)
 * - Infra Configuration (formatted with capitalization)
 * - Price (formatted with currency symbol)
 * - Actions (custom action buttons)
 *
 * @type {PlanTableColumn[]}
 */
export const getPlanTableColumns = (refetchPlans: () => void): PlanTableColumn[] => [
  {header: 'Plan name', accessorKey: 'name', id: 'name'},
  {
    header: 'Status',
    accessorKey: 'status',
    id: 'status',
    cell: (context: CellContext<PlanTableRow, unknown>) => {
      const status = context.getValue() as PlanStatus;
      const backgroundColor = getStatusColor(status);
      const color = getFontColor(status);
      const indicatorColor = getIndicatorColor(status);
      const label = getStatusLabel(status);
      return (
        <StatusChip label={label} backgroundColor={backgroundColor} indicatorColor={indicatorColor} color={color} />
      );
    },
  },
  {
    header: 'No. of devices',
    accessorKey: 'configureDevice',
    id: 'configureDevice',
    cell: ({row}: CellContext<PlanTableRow, unknown>) =>
      row.original.configureDevice ? `${row.original.configureDevice.min} - ${row.original.configureDevice.max}` : '-',
  },
  {
    header: 'Subscription Tenure',
    accessorKey: 'billingCycle',
    id: 'billingCycle',
    cell: ({row}: CellContext<PlanTableRow, unknown>) => capitalize(row.original.billingCycle?.cycleName) ?? '-',
  },
  {
    header: 'Infra Configuration',
    accessorKey: 'tier',
    id: 'tier',
    cell: ({row}: CellContext<PlanTableRow, unknown>) => capitalize(row.original.tier) ?? '-',
  },
  {
    header: 'Price',
    accessorKey: 'price',
    id: 'price',
    cell: ({row}: CellContext<PlanTableRow, unknown>) => {
      const {price} = row.original;
      const {currency} = row.original;
      return `${currency?.symbol ?? ''}${price}`;
    },
  },
  {
    header: 'Actions',
    cell: (cellContext: CellContext<PlanTableRow, unknown>) => (
      <ActionButtons row={cellContext as CellContext<unknown, unknown>} refetchPlans={refetchPlans} />
    ),
  },
];
