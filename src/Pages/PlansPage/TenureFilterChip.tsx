import {Box, CircularProgress, Typography} from '@mui/material';
import {forwardRef} from 'react';
import {useGetTenuresQuery} from 'redux/app/planManagementAPiSlice';
import {IBillingCycle} from 'redux/app/types/plan.type';
import {IClearFilters} from '../../Components/FilterUtil/IClearFilters';
import GenericFilterChip, {GenericFilterChipRef} from './GenericFilterChip';

export interface FilterStatusChipsProps {
  value?: Set<string>;
  onSelect?: (selected: Set<string>) => void;
}

export type FilterTenureChipsRef = IClearFilters;

/**
 * `TenureChips` is a React forwardRef component that renders a filter chip UI for selecting subscription tenures.
 * It fetches available tenures using the `useGetTenuresQuery` hook and displays a loading or error message as appropriate.
 * Once data is loaded, it renders a `GenericFilterChip` component with the fetched tenure options.
 *
 * @param {FilterStatusChipsProps} props - The props for the component, including `onSelect` callback and `value`.
 * @param {React.Ref<FilterTenureChipsRef>} ref - A ref forwarded to the underlying `GenericFilterChip` component.
 *
 * @returns {JSX.Element} The rendered filter chip component or a loading/error message.
 */
const TenureChips = forwardRef<FilterTenureChipsRef, FilterStatusChipsProps>(({onSelect, value}, ref) => {
  const {data, isLoading, error} = useGetTenuresQuery({});

  if (isLoading) {
    return (
      <Box display="flex" alignItems="center" justifyContent="center" height={40}>
        <CircularProgress size={24} />
      </Box>
    );
  }
  if (error) {
    return <Typography>Something Went Wrong</Typography>;
  }

  return (
    <GenericFilterChip
      ref={ref as React.Ref<GenericFilterChipRef>}
      title="Subscription tenure"
      data={data ?? {}}
      value={value}
      onSelect={onSelect}
      getKey={(item: IBillingCycle) => item.id}
      getLabel={(item: IBillingCycle) => String(item.cycleName)}
    />
  );
});
TenureChips.displayName = 'FilterTenureChips';

export default TenureChips;
