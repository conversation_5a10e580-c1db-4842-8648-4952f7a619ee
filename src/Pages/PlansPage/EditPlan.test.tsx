// @vitest-environment jsdom
import '@testing-library/jest-dom';
import {fireEvent, render, screen, waitFor} from '@testing-library/react';
import {vi} from 'vitest';
import EditPlan from './EditPlan';

// --- Mock state variables ---
let mockIsLoading = false;
let mockError = false;
let mockData: any = {
  id: '123',
  price: 100,
  costPerUser: 10,
  allowedUnlimitedUsers: false,
};
const mockNavigate = vi.fn();
const mockEnqueueSnackbar = vi.fn();
const mockUpdatePlanById = vi.fn();
const mockRefetch = vi.fn();

vi.mock('react-router-dom', () => ({
  useParams: () => ({id: '123'}),
  useNavigate: () => mockNavigate,
}));

vi.mock('notistack', () => ({
  useSnackbar: () => ({
    enqueueSnackbar: mockEnqueueSnackbar,
  }),
}));

vi.mock('redux/app/tenantManagementApiSlice', () => ({
  useGetPlanByIdQuery: () => ({
    data: mockData,
    isLoading: mockIsLoading,
    error: mockError,
    refetch: mockRefetch,
  }),
  useUpdatePlanByIdMutation: () => [mockUpdatePlanById],
}));

vi.mock('./AddPlan', () => ({
  __esModule: true,
  default: ({isEdit, initialValues, onSubmit}: any) => (
    <div>
      <div data-testid="add-plan-form">{isEdit ? 'Edit Mode' : 'Add Mode'}</div>
      <button data-testid="submit-btn" onClick={() => onSubmit(initialValues)}>
        Submit
      </button>
    </div>
  ),
}));

// Use the same mock path as AddPlan.test.tsx
vi.mock('Components/BackdropLoader/BackdropLoader', () => ({
  __esModule: true,
  default: () => <div data-testid="BackdropLoader" />,
}));

describe('EditPlan', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockIsLoading = false;
    mockError = false;
    mockData = {
      id: '123',
      price: 100,
      costPerUser: 10,
      allowedUnlimitedUsers: false,
    };
  });

  it('shows loader when loading', () => {
    mockIsLoading = true;
    render(<EditPlan />);
    expect(screen.getByTestId('BackdropLoader')).toBeInTheDocument();
  });

  it('shows error when plan fetch fails', () => {
    mockError = true;
    render(<EditPlan />);
    expect(screen.getByText(/Failed to load plan details/i)).toBeInTheDocument();
  });

  it('renders AddPlan in edit mode when data is loaded', () => {
    render(<EditPlan />);
    expect(screen.getByTestId('add-plan-form')).toHaveTextContent('Edit Mode');
  });

  it('submits form and shows success snackbar', async () => {
    mockUpdatePlanById.mockReturnValue({
      unwrap: () => Promise.resolve({}),
    });
    render(<EditPlan />);
    fireEvent.click(screen.getByTestId('submit-btn'));
    await waitFor(() => {
      expect(mockUpdatePlanById).toHaveBeenCalled();
      expect(mockEnqueueSnackbar).toHaveBeenCalledWith('Plan updated successfully!', {variant: 'success'});
      expect(mockNavigate).toHaveBeenCalledWith('/plans');
      expect(mockRefetch).toHaveBeenCalled();
    });
  });

  it('shows error snackbar on update failure', async () => {
    mockUpdatePlanById.mockReturnValue({
      unwrap: () => Promise.reject(new Error('fail')),
    });
    render(<EditPlan />);
    fireEvent.click(screen.getByTestId('submit-btn'));
    await waitFor(() => {
      expect(mockEnqueueSnackbar).toHaveBeenCalledWith('Failed to update plan', {variant: 'error'});
    });
  });
});
