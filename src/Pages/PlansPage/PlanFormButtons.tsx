import {Box, Grid, useTheme} from '@mui/material';
import Button from 'Components/Button';
import {FormikValues, useFormikContext} from 'formik';
import {Integers} from 'Helpers/integers';
import React from 'react';
import {buttonGridStyles, cancelButtonStyles, submitButtonStyles} from 'styles/pages/AddPlan.styles';

interface PlanFormButtonsProps {
  isEdit: boolean;
  onCancel: () => void;
  isLoading: boolean;
}

/**
 * Renders the action buttons for the plan form, including "Cancel" and "Submit" buttons.
 *
 * @param {Object} props - The component props.
 * @param {boolean} props.isEdit - Indicates if the form is in edit mode.
 * @param {() => void} props.onCancel - Callback function to handle cancel action.
 * @param {boolean} props.isLoading - Indicates if the submit action is loading.
 *
 * The submit button is disabled unless the form is valid and has been modified.
 * The button label changes based on whether the form is in edit mode.
 */
const PlanFormButtons: React.FC<PlanFormButtonsProps> = ({isEdit, onCancel, isLoading}) => {
  const {isValid, dirty} = useFormikContext<FormikValues>();

  const isFormValid = isValid && dirty;
  const theme = useTheme();

  return (
    <>
      <Grid size={{xs: 12}} sx={{mt: 3, mb: 2, padding: 0, margin: 0}}>
        <Box
          sx={{borderTop: `0.0625rem solid ${theme.palette.white[Integers.ThreeHundred]}`, width: '100%', padding: 0}}
        />
      </Grid>
      <Grid size={{xs: 12}} sx={buttonGridStyles}>
        <Button
          type="button"
          variant="outlined"
          color="secondary"
          data-testid="cancel-button"
          sx={cancelButtonStyles}
          onClick={onCancel}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          isLoading={isLoading}
          variant="contained"
          color="primary"
          sx={submitButtonStyles}
          disabled={!isFormValid}
          data-testid="submit-button"
        >
          {isEdit ? 'Update Plan' : 'Add Plan'}
        </Button>
      </Grid>
    </>
  );
};

export default PlanFormButtons;
