import AddIcon from '@mui/icons-material/Add';
import {Box, CircularProgress, Stack, Tooltip, Typography} from '@mui/material';
import {CellContext} from '@tanstack/react-table';
import ActivateIcon from 'Assets/ActivateIcon';
import DeactivateIcon from 'Assets/DeactivateIcon';
import EditIcon from 'Assets/EditIcon';
import EyeIcon from 'Assets/EyeIcon';
import SearchIcon from 'Assets/search-icon.svg';
import FilterIcon from 'Assets/tenant-filter-icon.svg';
import BlueButton from 'Components/BlueButton/BlueButton';
import BorderButton from 'Components/BorderButton/BorderButton';
import Button from 'Components/Button';
import SVGImageFromPath from 'Components/SVGImageFromPath';
import {DebouncedInput, Table} from 'Components/Table';
import {useSnackbar} from 'notistack';
import React, {useState} from 'react';
import {useNavigate} from 'react-router-dom';
import {useGetPlansCountQuery, useGetPlansQuery, useUpdateStatusMutation} from 'redux/app/planManagementAPiSlice';
import {PlanApiDTO, PlanApiForCountDTO} from 'redux/app/types/plan.type';
import {
  actionStyles,
  bodyCellProps,
  coloumnCellProps,
  headerBoxStyle,
  leftHeaderStyle,
  tableContainerProps,
  tableHeadProps,
  toolTipStyles,
} from 'styles/pages/TenantPage.styles';
import {ActivateDeactivateDialog} from './ActivateDeactivateDialog/ActivateDeactivateDialog';
import PlanFilter, {IPlanFilter} from './PlanFilter';
import {DEFAULT_LIMIT, DEFAULT_OFFSET, getBackendColumnName, getPlanTableColumns, PlanStatus} from './plans.utils';

interface IActionButtonsProps {
  row: CellContext<unknown, unknown>;
}

/**
 * Renders action buttons (View, Edit, Activate/Deactivate) for a plan row in the Plans table.
 *
 * @param props - The props for the ActionButtons component.
 * @param props.row - The row data containing plan information.
 * @returns A stack of action icons with tooltips for interacting with a plan.
 */
export const ActionButtons: React.FC<IActionButtonsProps & {refetchPlans: () => void}> = ({row, refetchPlans}) => {
  const navigate = useNavigate();
  const [openActivateDeactivate, setOpenActivateDeactivate] = useState(false);
  const [actionType, setActionType] = useState<string | null>(null);
  const {enqueueSnackbar} = useSnackbar();

  const [updateStatus, {isLoading: isUpdatingStatus}] = useUpdateStatusMutation();

  const handleDialog = (str: string) => {
    setOpenActivateDeactivate(true);
    setActionType(str);
  };

  const handleDialogClose = () => {
    setOpenActivateDeactivate(false);
  };

  const handleActivateDeactivate = (action: string, planId: string, planName: string) => {
    updateStatus({planId: planId, status: action === 'Activate' ? PlanStatus.ACTIVE : PlanStatus.INACTIVE})
      .then(() => {
        const actionType = action === 'Activate' ? 'activated' : 'deactivated';
        enqueueSnackbar(`Plan ${actionType} successfully!` + planName, {variant: 'success'});
        setOpenActivateDeactivate(false);
        refetchPlans();
      })
      .catch(() => {});
  };

  const planStatus = Number((row.row.original as {status: PlanStatus}).status);

  const handleRedirectToDetails = (planId: string) => {
    navigate(`/plans/${planId}`);
  };
  const handleEditPlan = (planId: string) => {
    if (planStatus !== 1) {
      navigate(`/plans/${planId}/edit`);
    }
  };

  return (
    <Stack display="flex" flexDirection="row">
      <Tooltip title="View details" placement="top" arrow slotProps={toolTipStyles}>
        <EyeIcon sx={actionStyles} onClick={() => handleRedirectToDetails((row.row.original as {id: string}).id)} />
      </Tooltip>
      <Tooltip
        title={planStatus === 1 ? 'Edit disabled for inactive plans' : 'Edit'}
        placement="top"
        arrow
        slotProps={toolTipStyles}
      >
        <span>
          <EditIcon
            sx={{
              ...actionStyles,
              color: planStatus === 1 ? 'body.400' : 'body.500',
              cursor: planStatus === 1 ? 'not-allowed' : 'pointer',
            }}
            data-testid="edit-button"
            onClick={() => handleEditPlan((row.row.original as {id: string}).id)}
            style={{pointerEvents: planStatus === 1 ? 'none' : 'auto'}}
          />
        </span>
      </Tooltip>
      {planStatus === PlanStatus.ACTIVE ? (
        <Tooltip title="Deactivate" placement="top" arrow slotProps={toolTipStyles}>
          <DeactivateIcon sx={actionStyles} data-testid="deactivate-icon" onClick={() => handleDialog('Deactivate')} />
        </Tooltip>
      ) : (
        <Tooltip title="Activate" placement="top" arrow slotProps={toolTipStyles}>
          <ActivateIcon sx={actionStyles} data-testid="activate-icon" onClick={() => handleDialog('Activate')} />
        </Tooltip>
      )}
      {actionType && (
        <ActivateDeactivateDialog
          actionType={actionType as 'Activate' | 'Deactivate'}
          onConfirm={() =>
            handleActivateDeactivate(
              actionType,
              (row.row.original as {id: string}).id,
              (row.row.original as {name: string}).name,
            )
          }
          open={openActivateDeactivate}
          onClose={handleDialogClose}
          isLoading={isUpdatingStatus}
          title={(row.row.original as {name: string}).name}
        />
      )}
    </Stack>
  );
};

const buttonHeight = '2.375rem';

/**
 * `PlanPage` is a React functional component that displays a paginated, sortable, and filterable list of plans.
 *
 * Features:
 * - Fetches and displays plans data from the backend with support for pagination, sorting, and searching.
 * - Allows filtering of plans by status and date range using a filter dialog.
 * - Provides UI controls for searching, filtering, and adding new plans.
 * - Displays loading indicators and error notifications for data fetching operations.
 * - Integrates with Material-UI components and custom UI elements for consistent styling.
 *
 * State Management:
 * - Manages pagination (`limit`, `offset`), sorting (`sortBy`), and search term (`searchTerm`).
 * - Handles filter state via `selectedITenantFilter` and manages the filter dialog open state.
 *
 * API Integration:
 * - Uses `useGetPlansQuery` and `useGetPlansCountQuery` hooks to fetch plans data and total count.
 * - Constructs API filter parameters based on current UI state.
 *
 * UI Composition:
 * - Renders a header with search, filter, and action buttons.
 * - Displays a table of plans with manual pagination and sorting.
 * - Shows a filter dialog for advanced filtering options.
 *
 * Error Handling:
 * - Displays error notifications using `enqueueSnackbar` when data fetching fails.
 *
 * Navigation:
 * - Provides navigation to the "Add Plan" page.
 *
 * @component
 */
const PlanPage: React.FC = () => {
  const {enqueueSnackbar} = useSnackbar();
  const [limit, setLimit] = useState(DEFAULT_LIMIT);
  const [searchTerm, setSearchTerm] = useState('');
  const [offset, setOffset] = useState(DEFAULT_OFFSET);
  const [sortBy, setSortBy] = useState<string>('createdOn DESC');

  const [selectedIPlanFilter, setSelectedIPlanFilter] = React.useState<IPlanFilter | undefined>(undefined);
  const filterButtonRef = React.useRef<HTMLButtonElement>(null);
  const [openFilter, setOpenFilter] = React.useState(false);

  const handleSortChange = (columnId: string, sort: boolean) => {
    // Map frontend column name to backend column name
    const backendColumnName = getBackendColumnName(columnId);
    const sortParam = `${backendColumnName} ${sort ? 'DESC' : 'ASC'}`;
    setSortBy(sortParam);
  };

  const handlePageChange = (page: number) => {
    const newOffset = (page - 1) * limit;
    setOffset(newOffset);
  };

  const handleRowsPerPageChange = (newLimit: number) => {
    setLimit(newLimit);
    setOffset(0); // Reset to first page when changing page size
  };

  // Build the filter object for the API call
  const filterParams: PlanApiDTO = {
    limit,
    offset,
    order: sortBy,
    searchValue: searchTerm,
    status: selectedIPlanFilter?.status ? Array.from(selectedIPlanFilter.status).map(Number) : undefined,
    billingCycleId: selectedIPlanFilter?.tenure ? Array.from(selectedIPlanFilter.tenure).map(String) : undefined,
  };

  // Build count filter (without limit/offset)
  const countFilterParams: PlanApiForCountDTO = {
    order: sortBy,
    searchValue: searchTerm,
    status: selectedIPlanFilter?.status ? Array.from(selectedIPlanFilter.status).map(Number) : undefined,
    billingCycleId: selectedIPlanFilter?.tenure ? Array.from(selectedIPlanFilter.tenure).map(String) : undefined,
  };

  const {
    data: plans,
    error: plansError,
    isFetching: isLoading,
    refetch: refetchPlans,
  } = useGetPlansQuery(filterParams, {
    refetchOnMountOrArgChange: true,
  });
  const {
    data: plansCount,
    error: plansCountError,
    isFetching: countLoading,
  } = useGetPlansCountQuery(countFilterParams, {
    refetchOnMountOrArgChange: true,
  });

  // Show error notifications
  React.useEffect(() => {
    if (plansError) {
      enqueueSnackbar('Failed to fetch plans data', {variant: 'error'});
    }
    if (plansCountError) {
      enqueueSnackbar('Failed to fetch plans count', {variant: 'error'});
    }
  }, [plansError, plansCountError, enqueueSnackbar]);
  // Trigger initial sort on mount
  // Removed redundant initial sort effect to prevent double API call

  const navigate = useNavigate();

  const handleRedirect = () => {
    navigate('/add-plan');
  };

  const buildFilterButton = () => {
    let filterSize = selectedIPlanFilter?.status?.size ?? 0;
    filterSize += selectedIPlanFilter?.tenure?.size ?? 0;

    const hasFilters = filterSize > 0;

    return (
      <Box sx={{position: 'relative', display: 'inline-block'}}>
        <BorderButton
          ref={filterButtonRef}
          sx={{
            height: buttonHeight,
            minWidth: buttonHeight,
            p: 0,
            borderColor: filterSize > 0 ? 'secondary.main' : 'body.100',
          }}
          onClick={() => setOpenFilter(true)}
        >
          <SVGImageFromPath path={FilterIcon} sx={{width: '0.8125rem', color: 'body.800'}} />
        </BorderButton>
        {/* Counter */}
        {hasFilters && (
          <Box
            sx={{
              position: 'absolute',
              top: '-15%',
              right: '-15%',
              backgroundColor: 'primary.main',
              color: '#fff',
              borderRadius: '50%',
              minWidth: 18,
              height: 18,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '0.75rem',
              fontWeight: 700,
              zIndex: 1,
              boxShadow: 1,
              px: 0.5,
            }}
          >
            {filterSize}
          </Box>
        )}
      </Box>
    );
  };

  const buildTopRightSection = () => {
    return (
      <Box sx={{display: 'flex', gap: 1, flexDirection: 'row', alignItems: 'center', ml: 'auto'}}>
        {/* Search Input */}
        <DebouncedInput
          placeholder="Search plan"
          data-testid="search-plan"
          sx={{
            fontSize: '0.675rem',
            pl: 0,
          }}
          debounceTime={500}
          leftAdornment={<SVGImageFromPath path={SearchIcon} sx={{width: '1rem', height: '1rem', mr: 1}} />}
          inputSx={{
            fontSize: '1rem',
            fontWeight: 400,
            color: 'body.dark',
          }}
          value={searchTerm}
          onChange={value => {
            setSearchTerm('' + value);
          }}
        />
        {buildFilterButton()}

        <Button
          sx={{
            height: buttonHeight,
            fontSize: '0.8125rem',
            fontWeight: 700,
            pl: 2,
            pr: 2.5,
            borderWidth: '0.0625rem',
            borderStyle: 'solid',
            borderColor: 'body.200',
            color: 'body.dark',
          }}
        >
          Manage Device Limits
        </Button>
        <BlueButton
          sx={{height: buttonHeight, fontSize: '0.8125rem', fontWeight: 700, pl: 2, pr: 2.5}}
          onClick={handleRedirect}
        >
          <Box sx={{flexDirection: 'row', display: 'flex', alignItems: 'center', gap: 1}}>
            <AddIcon sx={{height: '1rem', width: '1rem'}} />
            Add Plan
          </Box>
        </BlueButton>
      </Box>
    );
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={headerBoxStyle}>
        <Typography variant="h6" sx={leftHeaderStyle}>
          Plans
        </Typography>
        {buildTopRightSection()}
      </Box>
      <Box sx={{position: 'relative', minHeight: '200px'}}>
        <Table
          data={plans || []}
          columns={getPlanTableColumns(refetchPlans)}
          enableSorting={true}
          tablePropsObject={{
            tableHeadProps: {sx: tableHeadProps},
            columnCellProps: {sx: coloumnCellProps},
            tableContainerProps: {sx: tableContainerProps},
            bodyCellProps: {sx: bodyCellProps},
          }}
          limit={limit}
          setLimit={setLimit}
          offset={offset}
          setOffset={setOffset}
          count={plansCount?.count || 0}
          manualPagination={true}
          onSortChange={handleSortChange}
          onPageChange={handlePageChange}
          onRowsPerPageChange={handleRowsPerPageChange}
          data-testid="plan-table"
        />
        {(isLoading || countLoading) && (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              bgcolor: 'white.100',
              zIndex: 1,
            }}
          >
            <CircularProgress />
          </Box>
        )}
      </Box>
      {/* Tenant Filter */}
      <PlanFilter
        open={openFilter}
        value={selectedIPlanFilter}
        onClose={() => setOpenFilter(false)}
        anchorEl={filterButtonRef.current}
        onFilterChange={setSelectedIPlanFilter}
      />
    </Box>
  );
};

export default PlanPage;
