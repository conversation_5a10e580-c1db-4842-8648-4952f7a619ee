// PlanHistory.test.tsx
import '@testing-library/jest-dom';
import {render, screen} from '@testing-library/react';
import {afterEach, describe, expect, it, vi} from 'vitest';

vi.mock('./PlanHistoryCard', () => {
  const MockCard = (props: any) => <div data-testid="plan-card" data-props={JSON.stringify(props)} />;

  return {
    PlanHistoryCardCard: MockCard,
    PlanHistoryCard: MockCard,
    default: MockCard,
  };
});

import PlanHistory from './PlanHistory';

afterEach(() => {
  vi.clearAllMocks();
});

describe('PlanHistory (independent)', () => {
  it('renders one PlanHistoryCardCard with default/empty values when injectedValues is undefined', () => {
    render(<PlanHistory />);

    const cards = screen.getAllByTestId('plan-card');
    expect(cards).toHaveLength(1);

    const propsAttr = cards[0].getAttribute('data-props') || '{}';
    const props = JSON.parse(propsAttr);

    expect(props).toHaveProperty('data');
    expect(props.data).toMatchObject({
      name: '',
      numberOfDevices: '-',
      infraConfigurations: '',
      tenure: '',
      price: '',
      costPerUser: '',
      allowedUnlimited: false,
      version: '',
      createdAt: '',
      createdBy: '',
    });

    expect(props.updatedBy).toBe('');
    expect(props.index).toBe(0);
  });

  it('renders 1 + planHistories.length cards and maps fields correctly', () => {
    const injectedValues = {
      name: 'Pro Plan',
      configureDevice: {min: 2, max: 5},
      tier: 'cloud',
      billingCycle: {cycleName: 'MONTHLY'},
      price: 1200,
      costPerUser: 10.5,
      allowedUnlimitedUsers: false,
      version: 'v2.0',
      // prefer modifiedOn if present
      modifiedOn: '2025-07-10T10:00:00.000Z',
      createdOn: '2025-07-01T09:00:00.000Z',
      modifiedBy: 'user-mod',
      createdBy: 'user-create',
      planHistories: [
        {
          price: 900,
          costPerUser: 9,
          allowedUnlimitedUsers: false,
          version: 'v1.5',
          createdOn: '2025-06-01T08:00:00.000Z',
          createdBy: 'hist-user-1',
        },
        {
          price: 700,
          costPerUser: 7,
          allowedUnlimitedUsers: true,
          version: 'v1.0',
          createdOn: '2025-05-01T07:00:00.000Z',
          createdBy: 'hist-user-2',
        },
      ],
    };

    render(<PlanHistory injectedValues={injectedValues as any} />);

    const cards = screen.getAllByTestId('plan-card');
    // 1 current + 2 histories = 3
    expect(cards).toHaveLength(3);

    const parsed = cards.map(c => JSON.parse(c.getAttribute('data-props') || '{}'));

    // First card corresponds to current injectedValues mapping
    const firstData = parsed[0].data;
    expect(firstData.name).toBe('Pro Plan');
    expect(firstData.numberOfDevices).toBe('2-5'); // `${min}-${max}`
    expect(firstData.infraConfigurations).toBe('cloud');
    expect(firstData.tenure).toBe('MONTHLY');
    expect(firstData.price).toBe(`${injectedValues.price}`); // "1200"
    expect(firstData.costPerUser).toBe(`${injectedValues.costPerUser}`); // "10.5"
    expect(firstData.allowedUnlimited).toBe(false);
    expect(firstData.version).toBe(injectedValues.version);
    // createdAt should prefer modifiedOn
    expect(firstData.createdAt).toBe(injectedValues.modifiedOn);
    // createdBy should prefer modifiedBy
    expect(firstData.createdBy).toBe(injectedValues.modifiedBy);
    expect(parsed[0].updatedBy).toBe(injectedValues.modifiedBy);
    expect(parsed[0].index).toBe(0);

    // Second card corresponds to first history
    const secondData = parsed[1].data;
    expect(secondData.price).toBe(`${injectedValues.planHistories[0].price}`); // "900"
    expect(secondData.costPerUser).toBe(`${injectedValues.planHistories[0].costPerUser}`); // "9"
    expect(secondData.allowedUnlimited).toBe(false);
    expect(secondData.version).toBe(injectedValues.planHistories[0].version);
    expect(secondData.createdAt).toBe(injectedValues.planHistories[0].createdOn);
    expect(parsed[1].updatedBy).toBe(injectedValues.planHistories[0].createdBy);
    expect(parsed[1].index).toBe(1);

    // Third card corresponds to second history (which had allowedUnlimited true)
    const thirdData = parsed[2].data;
    expect(thirdData.price).toBe(`${injectedValues.planHistories[1].price}`); // "700"
    expect(thirdData.costPerUser).toBe(`${injectedValues.planHistories[1].costPerUser}`); // "7"
    expect(thirdData.allowedUnlimited).toBe(true);
    expect(thirdData.version).toBe(injectedValues.planHistories[1].version);
    expect(thirdData.createdAt).toBe(injectedValues.planHistories[1].createdOn);
    expect(parsed[2].updatedBy).toBe(injectedValues.planHistories[1].createdBy);
    expect(parsed[2].index).toBe(2);
  });
});
