import {Box, Typography} from '@mui/material';
import BackdropLoader from 'Components/BackdropLoader';
import {useSnackbar} from 'notistack';
import {useEffect, useState} from 'react';
import {useGetUserListsQuery, useGetUserQuery, useGetUsersCountQuery} from 'redux/auth/authApiSlice';
import {bodyCellProps, coloumnCellProps, tableContainerProps, tableHeadProps} from 'styles/pages/TenantPage.styles';
import {Table} from '../../Components/Table';
import UserListEmpty from './UserListEmpty';
import {DEFAULT_LIMIT, userColumns, UserTableRow} from './userManagement.utils';
export const headerBoxStyle = {
  pb: 1,
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
};

export const leftHeaderStyle = {
  fontWeight: 700,
  color: 'body.dark',
};

/**
 * UserManagementPage component displays a paginated, sortable table of users.
 * Handles data fetching, error notifications, and empty state rendering.
 *
 * @component
 * @returns {JSX.Element} The rendered user management page.
 */
const UserManagementPage = () => {
  // Number of rows per page
  const [limit, setLimit] = useState(DEFAULT_LIMIT);
  // Current offset for pagination
  const [offset, setOffset] = useState(0);

  // Snackbar for error notifications
  const {enqueueSnackbar} = useSnackbar();
  const {data: userData} = useGetUserQuery();

  // Fetch total user count
  const {data: usersCount, error: countError} = useGetUsersCountQuery(userData?.userTenantId ?? '');

  // Reset offset when limit changes
  useEffect(() => {
    setOffset(0);
  }, [limit]);

  // Query parameters for fetching users
  const params = {
    filter: {
      offset,
      limit,
      order: 'createdOn DESC',
      where: {
        userTenantId: {
          neq: userData?.userTenantId ?? '',
        },
      },
    },
  };

  // Fetch users data
  const {
    data: usersList = [],
    error: usersError,
    isLoading,
  } = useGetUserListsQuery(params.filter, {
    refetchOnMountOrArgChange: true,
  });

  // Show error notifications if data fetch fails
  useEffect(() => {
    if (usersError && !isLoading) {
      enqueueSnackbar('Failed to fetch user data', {variant: 'error'});
    }
  }, [usersError, isLoading, enqueueSnackbar]);

  useEffect(() => {
    if (countError && !isLoading) {
      enqueueSnackbar('Failed to fetch user count', {variant: 'error'});
    }
  }, [countError, isLoading, enqueueSnackbar]);

  /**
   * Handles page change event for pagination.
   * @param page - The new page number.
   */
  const handlePageChange = (page: number) => {
    const newOffset = (page - 1) * limit;
    setOffset(newOffset);
  };

  /**
   * Handles change in number of rows per page.
   * @param newLimit - The new limit value.
   */
  const handleRowsPerPageChange = (newLimit: number) => {
    setLimit(newLimit);
    setOffset(0);
  };

  return (
    <Box>
      {/* Header section */}
      <Box sx={headerBoxStyle}>
        <Typography variant="h6" sx={leftHeaderStyle}>
          User Management
        </Typography>
      </Box>
      {/* Table or empty state */}
      <Box sx={{position: 'relative', minHeight: '12.5rem'}}>
        {!isLoading && usersCount?.count === 0 ? (
          <UserListEmpty />
        ) : (
          <>
            <Table
              data={(usersList as UserTableRow[]) || []}
              columns={userColumns}
              enableSorting={true}
              tablePropsObject={{
                tableHeadProps: {sx: tableHeadProps},
                columnCellProps: {sx: coloumnCellProps},
                tableContainerProps: {sx: tableContainerProps},
                bodyCellProps: {sx: bodyCellProps},
                tableProps: {sx: {tableLayout: 'fixed', minWidth: 1000}},
              }}
              limit={limit}
              setLimit={setLimit}
              offset={offset}
              setOffset={setOffset}
              count={usersCount?.count ?? 0}
              manualPagination={true}
              onPageChange={handlePageChange}
              onRowsPerPageChange={handleRowsPerPageChange}
              data-testid="tenant-user-table"
            />
            {/* Loading overlay */}
            {isLoading && <BackdropLoader />}
          </>
        )}
      </Box>
    </Box>
  );
};

export default UserManagementPage;
