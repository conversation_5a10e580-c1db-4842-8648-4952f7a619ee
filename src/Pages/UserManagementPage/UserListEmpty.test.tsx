import {render, screen} from '@testing-library/react';
import {describe, expect, it} from 'vitest';
import UserListEmpty from './UserListEmpty';

const USER_IMAGE_ALT = 'No users found';
const MAIN_TITLE = 'No users yet!';
const SUB_TITLE = 'Add users and assign them roles to manage access and permissions.';
const BUTTON_TEXT = 'Add User';

describe('UserListEmpty', () => {
  beforeEach(() => {
    render(<UserListEmpty />);
  });

  it('renders the empty user image', () => {
    const img = screen.getByAltText(USER_IMAGE_ALT);
    expect(img).toBeInTheDocument();
  });

  it('renders the main title', () => {
    expect(screen.getByText(MAIN_TITLE)).toBeInTheDocument();
  });

  it('renders the subtitle', () => {
    expect(screen.getByText(/Add users and assign them roles to manage/)).toBeInTheDocument();
    expect(screen.getByText(/access and permissions\./)).toBeInTheDocument();
  });

  it('renders the add user button', () => {
    expect(screen.getByRole('button', {name: BUTTON_TEXT})).toBeInTheDocument();
  });

  it('renders the AddIcon inside the button', () => {
    const button = screen.getByRole('button', {name: BUTTON_TEXT});
    const svg = button.querySelector('svg');
    expect(svg).toBeInTheDocument();
  });
});
