import {Box, Tooltip} from '@mui/material';
import StatusChip from 'Components/StatusChip/StatusChip';
import {StatusChipState} from 'Components/StatusChip/statusChip.util';
import React from 'react';
import {toolTipStyles} from 'styles/pages/TenantPage.styles';
import ActionButtons from './components/ActionButton';
export {default as ActionButtons} from './components/ActionButton';

const whiteMain = 'white.main';

/**
 * Table row type for user management
 */
export interface UserTableRow {
  firstName: string;
  email: string;
  roleName: string;
  status: UserStatus;
  createdOn: string;
  modifiedOn: string;
}

/**
 * User status enumeration for users
 */
export enum UserStatus {
  PENDINGPROVISION,
  ACTIVE,
  INACTIVE,
}

/**
 * Default number of rows per page in user management table.
 */
export const DEFAULT_LIMIT = 5;

/**
 * Default offset for pagination in user management table.
 */
export const DEFAULT_OFFSET = 0;

/**
 * Returns the label for a given user status.
 * @param status - The user status or its numeric value.
 * @returns Status label string.
 */
export const getUserStatusLabel = (status: UserStatus | number): string => {
  const statusLabelMap: Record<UserStatus | number, string> = {
    [UserStatus.ACTIVE]: 'Active',
    [UserStatus.INACTIVE]: 'Inactive',
    [UserStatus.PENDINGPROVISION]: 'Pending activation',
  };
  return statusLabelMap[status] ?? 'Pending activation';
};

/**
 * Maps frontend column names to backend field names for sorting.
 */
const columnNameMap: Record<string, string> = {
  name: 'firstName',
  email: 'email',
  role: 'roleName',
  status: 'status',
  createdDate: 'createdOn',
  modifiedDate: 'modifiedOn',
};

const userStatusMapToStatusChip: Record<UserStatus, StatusChipState> = {
  [UserStatus.ACTIVE]: StatusChipState.ACTIVE,
  [UserStatus.PENDINGPROVISION]: StatusChipState.PENDINGPROVISION,
  [UserStatus.INACTIVE]: StatusChipState.INACTIVE,
};

/**
 * Returns the backend field name for a given frontend column name.
 * @param columnName - The frontend column name.
 * @returns Backend field name.
 */
export const getBackendColumnName = (columnName: string): string => columnNameMap[columnName] || columnName;

/**
 * Columns definition for the user management table.
 * Each column configures header, accessor key, cell renderer, and width.
 */
export const userColumns: Array<{
  accessorKey?: keyof UserTableRow;
  header: string;
  cell?: (context: {row: {original: UserTableRow}}) => React.ReactNode;
  width?: string;
}> = [
  {
    accessorKey: 'firstName',
    header: 'Name',
    // Renders user's name with tooltip and ellipsis for overflow
    cell: ({row}) => (
      <Tooltip title={row.original.firstName || '-'} placement="top" arrow slotProps={toolTipStyles}>
        <Box
          sx={{
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            display: 'inline-block',
            maxWidth: '100%',
          }}
          component="span"
        >
          {row.original.firstName || '-'}
        </Box>
      </Tooltip>
    ),
    width: '25%',
  },
  {
    accessorKey: 'email',
    header: 'Email Address',
    // Renders user's email with tooltip and ellipsis for overflow
    cell: ({row}) => (
      <Tooltip title={row.original.email || '-'} placement="top" arrow slotProps={toolTipStyles}>
        <Box
          sx={{
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            display: 'inline-block',
            maxWidth: '100%',
          }}
          component="span"
        >
          {row.original.email || '-'}
        </Box>
      </Tooltip>
    ),
    width: '38%',
  },
  {
    accessorKey: 'roleName',
    header: 'Role',
    // Renders user's role with tooltip
    cell: ({row}) => (
      <Tooltip title={row.original.roleName || '-'} placement="top" arrow slotProps={toolTipStyles}>
        <Box component="span">{row.original.roleName || '-'}</Box>
      </Tooltip>
    ),
    width: '10%',
  },
  {
    accessorKey: 'status',
    header: 'Status',
    // Renders user's status with colored StatusChip
    cell: ({row}) => {
      const status = row.original.status;
      return <StatusChip label={getUserStatusLabel(status)} status={userStatusMapToStatusChip[status]} />;
    },
    width: '10%',
  },
  {
    accessorKey: 'createdOn',
    header: 'Created On',
    // Renders formatted creation date with tooltip
    cell: ({row}) => {
      const date = new Date(row.original.createdOn);
      const parts = date
        .toLocaleDateString('en-GB', {
          day: '2-digit',
          month: 'short',
          year: 'numeric',
        })
        .split(' ');
      const formatted = `${parts[0]} ${parts[1].replace('.', '')}, ${parts[2]}`;
      return (
        <Tooltip title={formatted} placement="top" arrow slotProps={toolTipStyles}>
          <Box component="span">{formatted}</Box>
        </Tooltip>
      );
    },
    width: '2%',
  },
  {
    accessorKey: 'modifiedOn',
    header: 'Modified On',
    // Renders formatted modification date with tooltip
    cell: ({row}) => {
      const date = new Date(row.original.modifiedOn);
      const parts = date
        .toLocaleDateString('en-GB', {
          day: '2-digit',
          month: 'short',
          year: 'numeric',
        })
        .split(' ');
      const formatted = `${parts[0]} ${parts[1].replace('.', '')}, ${parts[2]}`;
      return (
        <Tooltip title={formatted} placement="top" arrow slotProps={toolTipStyles}>
          <Box component="span">{formatted}</Box>
        </Tooltip>
      );
    },
    width: '2%',
  },
  {
    header: 'Actions',
    // Renders action buttons for each row
    cell: ({row}) => <ActionButtons tableRow={row.original} />,
    width: '2%',
  },
];
