import {render, screen} from '@testing-library/react';
import {beforeEach, describe, expect, it, vi} from 'vitest';
import UserManagementPage from './UserManagementPage';

// Mocks
const enqueueSnackbar = vi.fn();
vi.mock('notistack', () => ({
  useSnackbar: () => ({enqueueSnackbar}),
}));
vi.mock('redux/auth/authApiSlice', async () => {
  const actual = await import('redux/auth/authApiSlice');
  return {
    ...actual,
    useGetUserListsQuery: vi.fn(),
    useGetTenantUserQuery: vi.fn(),
    useGetTenantUsersCountQuery: vi.fn(),
    useGetUserQuery: vi.fn(),
    useGetUsersCountQuery: vi.fn(),
  };
});
vi.mock('./UserListEmpty', () => ({
  default: () => <div data-testid="user-list-empty">Empty</div>,
}));
vi.mock('../../Components/Table', () => ({
  Table: ({data, columns, ...props}: any) => (
    <table data-testid="tenant-user-table">
      <tbody>
        {data.map((row: any, idx: number) => (
          <tr key={idx}>
            <td>{row.firstName}</td>
            <td>{row.email}</td>
          </tr>
        ))}
      </tbody>
    </table>
  ),
}));

const mockTenantUsers = [
  {firstName: 'John', email: '<EMAIL>', roleName: 'Admin', status: 1, createdOn: '', modifiedOn: ''},
];

// Static imports for mocked hooks
import {useGetUserListsQuery, useGetUserQuery, useGetUsersCountQuery} from 'redux/auth/authApiSlice';

describe('UserManagementPage', () => {
  // Removed duplicate local enqueueSnackbar mock to use the global instance

  beforeEach(() => {
    enqueueSnackbar.mockClear();
    (useGetUserListsQuery as unknown as {mockReturnValue: Function}).mockReturnValue({
      data: mockTenantUsers,
      error: undefined,
      isLoading: false,
    });
    (useGetUsersCountQuery as unknown as {mockReturnValue: Function}).mockReturnValue({
      data: {count: 1},
      error: undefined,
      isFetching: false,
    });
    (useGetUserQuery as unknown as {mockReturnValue: Function}).mockReturnValue({
      data: {userTenantId: 'tenant-id'},
      error: undefined,
      isFetching: false,
    });
  });

  it('renders header', () => {
    render(<UserManagementPage />);
    expect(screen.getByText('User Management')).toBeInTheDocument();
  });

  it('renders table when users exist', () => {
    render(<UserManagementPage />);
    expect(screen.getByTestId('tenant-user-table')).toBeInTheDocument();
    expect(screen.getByText('John')).toBeInTheDocument();
  });

  it('renders empty state when no users', () => {
    (useGetUsersCountQuery as any).mockReturnValue({
      data: {count: 0},
      error: undefined,
      isFetching: false,
    });
    render(<UserManagementPage />);
    expect(screen.getByTestId('user-list-empty')).toBeInTheDocument();
  });

  it('shows loading spinner when loading', () => {
    (useGetUserListsQuery as any).mockReturnValue({
      data: [],
      error: undefined,
      isLoading: true,
    });
    render(<UserManagementPage />);
    expect(screen.getByTestId('circularProgress')).toBeInTheDocument();
  });

  it('shows error snackbar for user error', () => {
    (useGetUserListsQuery as any).mockReturnValue({
      data: [],
      error: true,
      isLoading: false,
    });
    render(<UserManagementPage />);
    expect(enqueueSnackbar).toHaveBeenCalledWith('Failed to fetch user data', {variant: 'error'});
    // If you see issues, use: expect(require('notistack').useSnackbar().enqueueSnackbar).toHaveBeenCalledWith(...)
  });

  it('shows error snackbar for count error', () => {
    (useGetUsersCountQuery as any).mockReturnValue({
      data: {count: 1},
      error: true,
      isFetching: false,
    });
    render(<UserManagementPage />);
    expect(enqueueSnackbar).toHaveBeenCalledWith('Failed to fetch user count', {variant: 'error'});
  });
});
