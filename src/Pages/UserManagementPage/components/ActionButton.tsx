import {Box, Stack, Tooltip} from '@mui/material';
import ActivateIcon from 'Assets/ActivateIcon';
import DeactivateIcon from 'Assets/DeactivateIcon';
import EditIcon from 'Assets/EditIcon';
import EyeIcon from 'Assets/EyeIcon';
import LinkImage from 'Assets/link.svg';
import SVGImageFromPath from 'Components/SVGImageFromPath';
import {actionStyles, toolTipStyles} from 'styles/pages/TenantPage.styles';
import {UserStatus, UserTableRow} from '../userManagement.utils';

/**
 * Action buttons for user management table
 * @param param0 - The props for the component
 * @returns The action buttons for the user management table
 *
 */
const ActionButtons = ({tableRow}: {tableRow: UserTableRow}) => {
  const userStatus = tableRow.status;

  return (
    <Stack display="flex" flexDirection={'row'} data-testid="action-button">
      <Tooltip title="View details" placement="top" arrow slotProps={toolTipStyles}>
        <EyeIcon sx={actionStyles} data-testid="view-button" />
      </Tooltip>
      <Tooltip title="Edit" placement="top" arrow slotProps={toolTipStyles}>
        <EditIcon sx={actionStyles} data-testid="edit-button" />
      </Tooltip>
      {userStatus === UserStatus.INACTIVE && (
        <Tooltip title="Activate user" placement="top" arrow slotProps={toolTipStyles}>
          <ActivateIcon sx={actionStyles} data-testid="activate-button" />
        </Tooltip>
      )}
      {userStatus === UserStatus.ACTIVE && (
        <Tooltip title="Deactivate user" placement="top" arrow slotProps={toolTipStyles}>
          <DeactivateIcon sx={actionStyles} data-testid="deactivate-button" />
        </Tooltip>
      )}
      {userStatus !== UserStatus.ACTIVE && userStatus !== UserStatus.INACTIVE && (
        <Tooltip title="Send email link" placement="top" arrow slotProps={toolTipStyles}>
          <Box>
            <SVGImageFromPath path={LinkImage} sx={actionStyles} data-testid="link-button" />
          </Box>
        </Tooltip>
      )}
    </Stack>
  );
};

export default ActionButtons;
