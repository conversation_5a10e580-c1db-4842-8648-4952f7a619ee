import {render, screen} from '@testing-library/react';
import {describe, expect, it, vi} from 'vitest';
import {UserStatus, UserTableRow} from '../userManagement.utils';
import ActionButtons from './ActionButton';

// Mocks
vi.mock('react-router', () => ({
  useNavigate: () => vi.fn(),
}));
vi.mock('Assets/ActivateIcon', () => ({
  default: (props: any) => <div data-testid="activate-icon" {...props} />,
}));
vi.mock('Assets/DeactivateIcon', () => ({
  default: (props: any) => <div data-testid="deactivate-icon" {...props} />,
}));
vi.mock('Assets/EditIcon', () => ({
  default: (props: any) => <div data-testid="edit-icon" {...props} />,
}));
/* Removed EyeIcon mock since 'eye-icon' is not rendered in the DOM */
vi.mock('Assets/link.svg', () => ({
  default: 'link.svg',
}));
vi.mock('Components/SVGImageFromPath', () => ({
  default: (props: any) => <div data-testid="svg-image" {...props} />,
}));
vi.mock('styles/pages/TenantPage.styles', () => ({
  actionStyles: {},
  toolTipStyles: {},
}));

const baseRow: UserTableRow = {
  firstName: 'Test',
  email: '<EMAIL>',
  roleName: 'Admin',
  status: UserStatus.INACTIVE,
  createdOn: '',
  modifiedOn: '',
};

const allStatuses = Object.values(UserStatus).filter(v => typeof v === 'number') as UserStatus[];

describe('ActionButtons', () => {
  it('renders correct icons for all TenantUserStatus values', () => {
    allStatuses.forEach(status => {
      render(<ActionButtons tableRow={{...baseRow, status}} />);
      expect(screen.getByTestId('action-button')).toBeInTheDocument();
      expect(screen.getByTestId('view-button')).toBeInTheDocument();
      expect(screen.getByTestId('edit-button')).toBeInTheDocument();

      if (status === UserStatus.INACTIVE) {
        expect(screen.getByTestId('activate-button')).toBeInTheDocument();
      } else if (status === UserStatus.ACTIVE) {
        expect(screen.getByTestId('deactivate-button')).toBeInTheDocument();
      } else if (status === UserStatus.PENDINGPROVISION) {
        expect(screen.getByTestId('link-button')).toBeInTheDocument();
      }
      document.body.innerHTML = '';
    });
  });
});
