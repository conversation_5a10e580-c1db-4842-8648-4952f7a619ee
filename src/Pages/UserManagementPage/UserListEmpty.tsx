import AddIcon from '@mui/icons-material/Add';
import {Box, Typography} from '@mui/material';
import imgEmptyUser from 'Assets/empty-users.png';
import BlueButton from 'Components/BlueButton/BlueButton';
const buttonHeight = '2.375rem';
/**
 * UserListEmpty component
 * Displays a message and a button when there are no users in the list.
 * @returns {JSX.Element}
 */
const UserListEmpty = () => (
  <Box
    display="flex"
    flexDirection="column"
    alignItems="center"
    justifyContent="center"
    sx={{
      width: '100%',
      height: '80vh',
      position: 'relative',
      textAlign: 'center',
      gap: 0,
    }}
  >
    <Box component="img" src={imgEmptyUser} alt="No users found" sx={{width: '10.68rem', height: '10.68rem'}} />
    <Typography
      sx={{
        fontSize: '1.37rem',
        fontWeight: 700,
        color: 'body.dark',
        mt: 2,
      }}
    >
      No users yet!
    </Typography>
    <Typography
      sx={{
        fontSize: '1rem',
        fontWeight: 400,
        color: 'body.500',
        mt: 1.75,
      }}
    >
      Add users and assign them roles to manage
      <br /> access and permissions.
    </Typography>
    <BlueButton sx={{height: buttonHeight, fontSize: '1rem', fontWeight: 600, px: 3.5, py: 1.75, mt: 3}}>
      <Box sx={{flexDirection: 'row', display: 'flex', alignItems: 'center', gap: 1}}>
        <AddIcon sx={{height: '1rem', width: '1rem'}} />
        Add User
      </Box>
    </BlueButton>
  </Box>
);

export default UserListEmpty;
