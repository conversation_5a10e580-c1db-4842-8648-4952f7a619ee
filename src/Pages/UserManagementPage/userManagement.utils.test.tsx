import {describe, expect, it} from 'vitest';
import {
  DEFAULT_LIMIT,
  DEFAULT_OFFSET,
  UserStatus,
  UserTableRow,
  getBackendColumnName,
  getUserStatusLabel,
  userColumns,
} from './userManagement.utils';

describe('userManagement.utils', () => {
  it('TenantUserStatus enum values', () => {
    expect(UserStatus.PENDINGPROVISION).toBe(0);
    expect(UserStatus.ACTIVE).toBe(1);
    expect(UserStatus.INACTIVE).toBe(2);
  });

  it('getUserStatusLabel returns correct label', () => {
    expect(getUserStatusLabel(UserStatus.ACTIVE)).toBe('Active');
    expect(getUserStatusLabel(UserStatus.INACTIVE)).toBe('Inactive');
    expect(getUserStatusLabel(UserStatus.PENDINGPROVISION)).toBe('Pending activation');
    expect(getUserStatusLabel(999)).toBe('Pending activation');
  });

  it('getBackendColumnName returns mapped name', () => {
    expect(getBackendColumnName('name')).toBe('firstName');
    expect(getBackendColumnName('email')).toBe('email');
    expect(getBackendColumnName('role')).toBe('roleName');
    expect(getBackendColumnName('status')).toBe('status');
    expect(getBackendColumnName('createdDate')).toBe('createdOn');
    expect(getBackendColumnName('modifiedDate')).toBe('modifiedOn');
    expect(getBackendColumnName('unknown')).toBe('unknown');
  });

  it('DEFAULT_LIMIT and DEFAULT_OFFSET values', () => {
    expect(DEFAULT_LIMIT).toBe(5);
    expect(DEFAULT_OFFSET).toBe(0);
  });

  it('tenantUserColumns structure and cell functions', () => {
    expect(Array.isArray(userColumns)).toBe(true);
    expect(userColumns.length).toBeGreaterThan(0);
    userColumns.forEach(col => {
      expect(col.header).toBeDefined();
      if (col.cell) {
        // Check cell function returns a React element
        const row: {original: UserTableRow} = {
          original: {
            firstName: 'Test',
            email: '<EMAIL>',
            roleName: 'Admin',
            status: UserStatus.ACTIVE,
            createdOn: '2023-01-01',
            modifiedOn: '2023-01-02',
          },
        };
        const result = col.cell({row});
        expect(result).toBeTruthy();
      }
    });
  });
});
