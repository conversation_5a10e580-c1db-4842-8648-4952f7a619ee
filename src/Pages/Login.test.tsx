// src/Pages/Login.test.tsx
import {fireEvent, render, screen, waitFor} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import {MemoryRouter} from 'react-router-dom';
import {beforeEach, describe, expect, it, vi} from 'vitest';
import Login from './Login';

// Deep theme mock with body.900 and other palette keys
import {ThemeProvider, createTheme} from '@mui/material/styles';
const themeMock = createTheme({
  palette: {
    white: {main: '#fff'},
    primary: {main: '#1976d2'},
    error: {main: '#d32f2f'},
    warning: {main: '#ed6c02'},
    info: {main: '#0288d1'},
    success: {main: '#2e7d32'},
    grey: {500: '#9e9e9e'},
    body: {900: '#222', 800: '#333', 700: '#444', 200: '#bbb', dark: '#000'},
    common: {black: '#000', white: '#fff'},
    background: {default: '#fafafa', paper: '#fff'},
    text: {primary: '#000', secondary: '#666'},
    divider: '#e0e0e0',
  },
});
vi.mock('@mui/material/styles', async importOriginal => ({
  ...(await importOriginal()),
  useTheme: () => themeMock,
}));
vi.mock('@mui/material', async importOriginal => ({
  ...(await importOriginal()),
  useTheme: () => themeMock,
}));
vi.mock('src/config/theme', () => ({
  default: themeMock,
}));
vi.mock('src/Providers/theme/default', () => ({
  default: themeMock,
}));

// Mock react-router-dom hooks
const mockNavigate = vi.fn();
const mockLocation = {state: {from: {pathname: '/dashboard'}}};
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useLocation: () => mockLocation,
    MemoryRouter: actual.MemoryRouter,
  };
});

// Mock useAuth
const mockLogin = vi.fn();
let loginLoading = false;
vi.mock('Hooks/useAuth', () => ({
  __esModule: true,
  default: () => ({
    login: mockLogin,
    loginLoading,
    isLoggedIn: false,
    authData: {
      accessToken: null,
      refreshToken: null,
      expires: null,
      isLoggedIn: false,
    },
    logout: vi.fn(),
    logoutLoading: false,
  }),
}));

const renderLogin = () =>
  render(
    <ThemeProvider theme={themeMock}>
      <MemoryRouter>
        <Login />
      </MemoryRouter>
    </ThemeProvider>,
  );

describe('Login Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockLogin.mockReset();
    mockNavigate.mockReset();
  });

  it('renders all form fields and button', () => {
    renderLogin();
    expect(screen.getByPlaceholderText('Enter your email address')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Enter your password')).toBeInTheDocument();
    expect(screen.getByRole('button', {name: /sign in/i})).toBeInTheDocument();
    expect(screen.getByText('Forgot password?')).toBeInTheDocument();
  });

  it('shows validation errors for empty fields', async () => {
    renderLogin();
    const email = screen.getByPlaceholderText('Enter your email address');
    const password = screen.getByPlaceholderText('Enter your password');
    await userEvent.click(email);
    await userEvent.tab();
    await userEvent.click(password);
    await userEvent.tab();
    expect(await screen.findByText('Email is required')).toBeInTheDocument();
    expect(await screen.findByText('Password is required')).toBeInTheDocument();
  });

  it('shows validation error for invalid email', async () => {
    renderLogin();
    const email = screen.getByPlaceholderText('Enter your email address');
    await userEvent.type(email, 'invalid');
    await userEvent.tab();
    expect(await screen.findByText('Please enter a valid email address')).toBeInTheDocument();
  });

  it('shows validation error for short password', async () => {
    renderLogin();
    const password = screen.getByPlaceholderText('Enter your password');
    await userEvent.type(password, 'short');
    await userEvent.tab();
    expect(await screen.findByText('Password must be at least 8 characters')).toBeInTheDocument();
  });

  it('shows validation error for missing uppercase', async () => {
    renderLogin();
    const password = screen.getByPlaceholderText('Enter your password');
    await userEvent.type(password, 'password123!');
    await userEvent.tab();
    expect(await screen.findByText('Password must contain at least one uppercase letter')).toBeInTheDocument();
  });

  it('shows validation error for missing special character', async () => {
    renderLogin();
    const password = screen.getByPlaceholderText('Enter your password');
    await userEvent.type(password, 'Password123');
    await userEvent.tab();
    expect(
      await screen.findByText('Password must contain at least one special character (@$!%*?&)'),
    ).toBeInTheDocument();
  });

  it('calls login on valid form submit', async () => {
    mockLogin.mockResolvedValue({success: true, message: null});
    renderLogin();
    const email = screen.getByPlaceholderText('Enter your email address');
    const password = screen.getByPlaceholderText('Enter your password');
    await userEvent.type(email, '<EMAIL>');
    await userEvent.type(password, 'Password123!');
    fireEvent.submit(screen.getByRole('form'));
    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith({
        username: '<EMAIL>',
        password: 'Password123!',
      });
    });
  });

  it('shows error message on login failure', async () => {
    mockLogin.mockResolvedValue({success: false, message: 'Login failed'});
    renderLogin();
    const email = screen.getByPlaceholderText('Enter your email address');
    const password = screen.getByPlaceholderText('Enter your password');
    await userEvent.type(email, '<EMAIL>');
    await userEvent.type(password, 'Password123!');
    fireEvent.submit(screen.getByRole('form'));
    expect(await screen.findByText('Login failed')).toBeInTheDocument();
  });

  it('shows special error for invalid credentials', async () => {
    mockLogin.mockResolvedValue({success: false, message: 'Invalid Credentials'});
    renderLogin();
    const email = screen.getByPlaceholderText('Enter your email address');
    const password = screen.getByPlaceholderText('Enter your password');
    await userEvent.type(email, '<EMAIL>');
    await userEvent.type(password, 'Password123!');
    fireEvent.submit(screen.getByRole('form'));
    expect(await screen.findByText('Invalid credentials. Please try again.')).toBeInTheDocument();
  });

  it('navigates to custom location after successful login', async () => {
    mockLogin.mockResolvedValue({success: true, message: null});
    renderLogin();
    const email = screen.getByPlaceholderText('Enter your email address');
    const password = screen.getByPlaceholderText('Enter your password');
    await userEvent.type(email, '<EMAIL>');
    await userEvent.type(password, 'Password123!');
    fireEvent.submit(screen.getByRole('form'));
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/dashboard', {replace: true});
    });
  });

  it('shows loading state when loginLoading is true', () => {
    loginLoading = true;
    renderLogin();
    expect(screen.getByRole('button', {name: /signing in/i})).toBeInTheDocument();
    loginLoading = false;
  });
});
