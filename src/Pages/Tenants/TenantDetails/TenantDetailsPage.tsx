import {<PERSON>, Button, Chip, Container, Typography, useTheme} from '@mui/material';
import Breadcrumb from 'Components/Breadcrumb/Breadcrumb';
import {Integers} from 'Helpers/integers';
import React from 'react';
import {useNavigate, useParams} from 'react-router-dom';
import {useGetTenantByIdQuery} from 'redux/app/tenantManagementApiSlice';
import {IFile, TenantType} from 'redux/app/types';
import ThreeDotsIcon from '../../../Assets/dot.svg';
import EditIcon from '../../../Assets/edit.svg';
import ObservabilityIcon from '../../../Assets/observability.svg';
import CloseButton from '../../../Components/CloseButton/CloseButton';
import DocumentItem from '../../../Components/DocumentItem/DocumentItem';
import SVGImageFromPath from '../../../Components/SVGImageFromPath';
import {usStates} from '../AddTenant/addTenantsUtils';
import {getFontColor, getIndicatorColor, getStatusColor, getStatusLabel, TenantStatus} from '../tenants.utils';

/**
 * Component to render loading state
 */
const LoadingState: React.FC = () => (
  <Container maxWidth={false} sx={{maxWidth: '75rem', padding: '0.5rem', margin: '0 auto'}}>
    <Box sx={{display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px'}}>
      <Typography>Loading tenant details...</Typography>
    </Box>
  </Container>
);

/**
 * Component to render error state
 */
const ErrorState: React.FC = () => (
  <Container maxWidth={false} sx={{maxWidth: '75rem', padding: '0.5rem', margin: '0 auto'}}>
    <Box sx={{display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px'}}>
      <Typography color="error">Error loading tenant details. Please try again.</Typography>
    </Box>
  </Container>
);

/**
 * Component to render not found state
 */
const NotFoundState: React.FC = () => (
  <Container maxWidth={false} sx={{maxWidth: '75rem', padding: '0.5rem', margin: '0 auto'}}>
    <Box sx={{display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px'}}>
      <Typography>Tenant not found.</Typography>
    </Box>
  </Container>
);

/**
 * TenantDetailsPage component - matches Figma design exactly
 * Displays comprehensive tenant information with proper styling
 */
const TenantDetailsPage: React.FC = () => {
  const {tenantId} = useParams<{tenantId: string}>();

  // Fetch tenant data using the API
  // const { data: tenant, isLoading, error } = useGetTenantByIdQuery(id!, {
  //     skip: !id, // Skip the query if no ID is provided
  // });
  const {
    data: tenant,
    isLoading,
    error,
  } = useGetTenantByIdQuery(tenantId || '', {
    skip: !tenantId, // Skip the query if no ID is provided
  });

  // Early returns to reduce complexity
  if (isLoading) return <LoadingState />;
  if (error) return <ErrorState />;
  if (!tenant) return <NotFoundState />;

  // Get documents data from API tenant object
  const documentsData = tenant.files || [];

  return <TenantDetailsContent tenant={tenant} tenantId={tenantId} documentsData={documentsData} />;
};

/**
 * Helper functions extracted from main component
 */
const formatDate = (dateString?: string) => {
  if (!dateString) return 'N/A';
  try {
    return new Date(dateString).toLocaleDateString('en-US', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    });
  } catch {
    return 'N/A';
  }
};

// Constants for phone number formatting
const PHONE_NUMBER_LENGTH = 10;
const AREA_CODE_START = 0;
const AREA_CODE_END = 3;
const EXCHANGE_START = 3;
const EXCHANGE_END = 6;
const SUBSCRIBER_START = 6;

const formatPhoneNumber = (phone?: string) => {
  if (!phone) return 'N/A';
  // Remove all non-digit characters
  const digits = phone.replace(/\D/g, '');

  // Format as (XXX)XXX-XXXX for 10-digit numbers
  if (digits.length === PHONE_NUMBER_LENGTH) {
    return `(${digits.slice(AREA_CODE_START, AREA_CODE_END)})${digits.slice(EXCHANGE_START, EXCHANGE_END)}-${digits.slice(SUBSCRIBER_START)}`;
  }

  // Return original if not 10 digits
  return phone;
};

const capitalizeWords = (text?: string) => {
  if (!text) return 'N/A';
  return text
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
};

const getFullStateName = (stateAbbreviation?: string) => {
  if (!stateAbbreviation) return 'N/A';
  const state = usStates.find(state => state.value === stateAbbreviation.toUpperCase());
  return state ? state.label : stateAbbreviation;
};

/**
 * Main content component for tenant details
 */
interface TenantDetailsContentProps {
  tenant: TenantType;
  tenantId: string | undefined;
  documentsData: IFile[];
}

const TenantDetailsContent: React.FC<TenantDetailsContentProps> = ({tenant, tenantId, documentsData}) => {
  const theme = useTheme();
  const navigate = useNavigate();

  // Custom breadcrumb items for tenant details page
  const breadcrumbItems = [
    {label: 'Tenants', url: '/tenants'},
    {label: 'Tenant Details', url: `/tenants/${tenantId}`},
  ];

  const handleEdit = () => {
    //functionality to handle edit action
  };

  const handleObservability = () => {
    //functionality to handle observability action
  };

  const handleClose = () => {
    //functionality to handle close action
    navigate('/tenants');
  };

  const handleDocumentClick = (file: IFile) => {
    // Open file in new tab for viewing (not downloading)
    if (file.url && file.url !== '#') {
      // If direct URL is available, open it in new tab
      window.open(file.url, '_blank', 'noopener,noreferrer'); // NOSONAR
    } else if (file.fileKey) {
      // If no direct URL but fileKey exists, construct the URL
      // You may need to adjust this based on your API structure
      const fileUrl = `/api/files/${file.fileKey}`;
      window.open(fileUrl, '_blank', 'noopener,noreferrer'); // NOSONAR
    } else {
      // Fallback: try to open using a constructed URL
      // This might need adjustment based on your backend API
      window.open(`/api/tenants/${file.tenantId}/files/${file.id}`, '_blank', 'noopener,noreferrer'); // NOSONAR
    }
  };

  return (
    <Container
      maxWidth={false}
      sx={{
        maxWidth: '75rem',
        padding: '0.5rem',
        margin: '0 auto',
      }}
    >
      {/* Header Section */}
      <Box sx={{marginBottom: '1rem'}}>
        {/* Title, Status and Action Buttons Row */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'flex-start',
            justifyContent: 'space-between',
            marginBottom: '0.5rem',
          }}
        >
          {/* Left side - Title, Status and Breadcrumb */}
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: '0.5rem',
            }}
          >
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.75rem',
              }}
            >
              <Typography
                variant="h4"
                sx={{
                  fontSize: '1.125rem',
                  fontWeight: 600,
                  color: theme.palette.text.primary,
                  lineHeight: 1.2,
                }}
              >
                {tenant.tenantDetails.name || 'Unnamed Tenant'}
              </Typography>
              <Chip
                label={getStatusLabel(tenant.tenantDetails.status as unknown as TenantStatus)}
                sx={{
                  backgroundColor: getStatusColor(tenant.tenantDetails.status as unknown as TenantStatus),
                  color: getFontColor(tenant.tenantDetails.status as unknown as TenantStatus),
                  fontSize: '0.75rem',
                  fontWeight: 500,
                  height: '1.5rem',
                  borderRadius: '0.375rem',
                  '& .MuiChip-label': {
                    padding: '0 0.5rem',
                  },
                  '&::before': {
                    content: '""',
                    width: '0.375rem',
                    height: '0.375rem',
                    backgroundColor: getIndicatorColor(tenant.tenantDetails.status as unknown as TenantStatus),
                    borderRadius: '50%',
                    marginRight: '0.25rem',
                  },
                }}
              />
            </Box>
            <Breadcrumb items={breadcrumbItems} />
          </Box>

          {/* Right side - Action Buttons */}
          <Box
            sx={{
              display: 'flex',
              gap: '0.75rem',
              alignItems: 'flex-start',
              // marginTop: '0.25rem', // Slight adjustment to align with text baseline
            }}
          >
            <Button
              variant="outlined"
              startIcon={
                <SVGImageFromPath
                  path={EditIcon}
                  sx={{
                    width: '1rem',
                    height: '1rem',
                    color: theme.palette.text.primary,
                  }}
                />
              }
              onClick={handleEdit}
              sx={{
                borderRadius: '0.375rem',
                padding: '0.5rem 1rem',
                fontSize: '0.875rem',
                fontWeight: 500,
                textTransform: 'none',
                borderColor: theme.palette.grey[Integers.ThreeHundred],
                color: theme.palette.text.primary,
                '&:hover': {
                  borderColor: theme.palette.grey[Integers.FourHundred],
                  backgroundColor: theme.palette.grey[50],
                },
              }}
            >
              Edit
            </Button>
            <Button
              variant="outlined"
              startIcon={
                <SVGImageFromPath
                  path={ObservabilityIcon}
                  sx={{
                    width: '1rem',
                    height: '1rem',
                    color: theme.palette.text.primary,
                  }}
                />
              }
              onClick={handleObservability}
              sx={{
                borderRadius: '0.375rem',
                padding: '0.5rem 1rem',
                fontSize: '0.875rem',
                fontWeight: 500,
                textTransform: 'none',
                borderColor: theme.palette.grey[Integers.ThreeHundred],
                color: theme.palette.text.primary,
                '&:hover': {
                  borderColor: theme.palette.grey[Integers.FourHundred],
                  backgroundColor: theme.palette.grey[50],
                },
              }}
            >
              Observability
            </Button>
            <Button
              variant="outlined"
              sx={{
                borderRadius: '0.375rem',
                padding: '0.5rem 0.1rem !important',
                fontSize: '0.875rem',
                fontWeight: 500,
                textTransform: 'none',
                borderColor: theme.palette.grey[Integers.ThreeHundred],
                color: theme.palette.text.primary,
                '&:hover': {
                  borderColor: theme.palette.grey[Integers.FourHundred],
                  backgroundColor: theme.palette.grey[50],
                },
              }}
            >
              <SVGImageFromPath
                path={ThreeDotsIcon}
                sx={{
                  width: '1rem',
                  height: '1.6rem',
                  color: 'body.500',
                }}
              />
            </Button>
          </Box>
        </Box>
      </Box>

      {/* Content Cards */}
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: '1.5rem',
        }}
      >
        {/* Main Information Container - All sections in one card */}
        <Box
          sx={{
            backgroundColor: theme.palette.white.main,
            borderRadius: '0.75rem',
            padding: '1.5rem',
            border: `1px solid ${theme.palette.body[Integers.TwoHundred]}`,
          }}
        >
          {/* Tenant Information Section */}
          <Typography
            variant="h6"
            sx={{
              fontSize: '1.0rem',
              fontWeight: 600,
              color: theme.palette.text.primary,
              marginBottom: '1.5rem',
              lineHeight: 1.25,
            }}
          >
            Tenant information
          </Typography>

          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: 'repeat(3, 1fr)',
              gap: '2rem',
              marginBottom: '2rem',
            }}
          >
            <Box>
              <Typography
                sx={{
                  fontSize: '0.875rem',
                  color: 'body.500',
                  marginBottom: '0.5rem',
                  fontWeight: 400,
                }}
              >
                Created date
              </Typography>
              <Typography
                sx={{
                  fontSize: '0.875rem',
                  color: theme.palette.text.primary,
                  fontWeight: 500,
                }}
              >
                {formatDate(tenant.tenantDetails.createdOn)}
              </Typography>
            </Box>

            <Box>
              <Typography
                sx={{
                  fontSize: '0.875rem',
                  color: 'body.500',
                  marginBottom: '0.5rem',
                  fontWeight: 400,
                }}
              >
                Billing date
              </Typography>
              <Typography
                sx={{
                  fontSize: '0.875rem',
                  color: theme.palette.text.primary,
                  fontWeight: 500,
                }}
              >
                -
              </Typography>
            </Box>

            <Box>
              <Typography
                sx={{
                  fontSize: '0.875rem',
                  color: 'body.500',
                  marginBottom: '0.5rem',
                  fontWeight: 400,
                }}
              >
                Subdomain
              </Typography>
              <Typography
                sx={{
                  fontSize: '0.875rem',
                  color: theme.palette.text.primary,
                  fontWeight: 500,
                }}
              >
                {tenant.tenantDetails.key?.toLowerCase().replace(/\s+/g, '-') || 'N/A'}
              </Typography>
            </Box>
          </Box>

          {/* Separator */}
          <Box
            sx={{
              height: '1px',
              backgroundColor: theme.palette.grey[Integers.TwoHundred],
              marginBottom: '2rem',
            }}
          />

          {/* Plan Details Section */}
          <Typography
            variant="h6"
            sx={{
              fontSize: '1.0rem',
              fontWeight: 600,
              color: theme.palette.text.primary,
              marginBottom: '1.5rem',
              lineHeight: 1.25,
            }}
          >
            Plan details
          </Typography>

          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: 'repeat(4, 1fr)', // NOSONAR
              gap: '2rem',
              marginBottom: '2rem',
            }}
          >
            <Box>
              <Typography
                sx={{
                  fontSize: '0.875rem',
                  color: 'body.500',
                  marginBottom: '0.5rem',
                  fontWeight: 400,
                }}
              >
                Plan name
              </Typography>
              <Typography
                sx={{
                  fontSize: '0.875rem',
                  color: theme.palette.text.primary,
                  fontWeight: 500,
                }}
              >
                {'N/A'}
              </Typography>
            </Box>

            <Box>
              <Typography
                sx={{
                  fontSize: '0.875rem',
                  color: 'body.500',
                  marginBottom: '0.5rem',
                  fontWeight: 400,
                }}
              >
                Billing Cycle
              </Typography>
              <Typography
                sx={{
                  fontSize: '0.875rem',
                  color: theme.palette.text.primary,
                  fontWeight: 500,
                }}
              >
                {'N/A'}
              </Typography>
            </Box>

            <Box>
              <Typography
                sx={{
                  fontSize: '0.875rem',
                  color: 'body.500',
                  marginBottom: '0.5rem',
                  fontWeight: 400,
                }}
              >
                Pricing
              </Typography>
              <Typography
                sx={{
                  fontSize: '0.875rem',
                  color: theme.palette.text.primary,
                  fontWeight: 500,
                }}
              >
                {'N/A'}
              </Typography>
            </Box>

            <Box>
              <Typography
                sx={{
                  fontSize: '0.875rem',
                  color: 'body.500',
                  marginBottom: '0.5rem',
                  fontWeight: 400,
                }}
              >
                Status
              </Typography>
              <Chip
                label={getStatusLabel(tenant.tenantDetails.status as unknown as TenantStatus)}
                sx={{
                  backgroundColor: getStatusColor(tenant.tenantDetails.status as unknown as TenantStatus),
                  color: getFontColor(tenant.tenantDetails.status as unknown as TenantStatus),
                  fontSize: '0.75rem',
                  fontWeight: 500,
                  height: '1.5rem',
                  borderRadius: '0.375rem',
                  '& .MuiChip-label': {
                    padding: '0 0.5rem',
                  },
                  '&::before': {
                    content: '""',
                    width: '0.375rem',
                    height: '0.375rem',
                    backgroundColor: getIndicatorColor(tenant.tenantDetails.status as unknown as TenantStatus),
                    borderRadius: '50%',
                    marginRight: '0.25rem',
                  },
                }}
              />
            </Box>
          </Box>

          {/* Separator */}
          <Box
            sx={{
              height: '1px',
              backgroundColor: theme.palette.grey[Integers.TwoHundred],
              marginBottom: '2rem',
            }}
          />

          {/* Contact Information Section */}
          <Typography
            variant="h6"
            sx={{
              fontSize: '1.0rem',
              fontWeight: 600,
              color: theme.palette.text.primary,
              marginBottom: '1.5rem',
              lineHeight: 1.25,
            }}
          >
            Contact information
          </Typography>

          {/* First Row */}
          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: 'repeat(4, 1fr)',
              gap: '2rem',
              marginBottom: '1.5rem',
            }}
          >
            <Box>
              <Typography
                sx={{
                  fontSize: '0.875rem',
                  color: 'body.500',
                  marginBottom: '0.5rem',
                  fontWeight: 400,
                }}
              >
                Name
              </Typography>
              <Typography
                sx={{
                  fontSize: '0.875rem',
                  color: theme.palette.text.primary,
                  fontWeight: 500,
                }}
              >
                {tenant.contact?.firstName && tenant.contact?.lastName
                  ? `${capitalizeWords(tenant.contact.firstName)} ${capitalizeWords(tenant.contact.lastName)}`
                  : 'N/A'}
              </Typography>
            </Box>

            <Box>
              <Typography
                sx={{
                  fontSize: '0.875rem',
                  color: 'body.500',
                  marginBottom: '0.5rem',
                  fontWeight: 400,
                }}
              >
                Job title
              </Typography>
              <Typography
                sx={{
                  fontSize: '0.875rem',
                  color: theme.palette.text.primary,
                  fontWeight: 500,
                }}
              >
                {capitalizeWords(tenant.contact?.designation)}
              </Typography>
            </Box>

            <Box>
              <Typography
                sx={{
                  fontSize: '0.875rem',
                  color: 'body.500',
                  marginBottom: '0.5rem',
                  fontWeight: 400,
                }}
              >
                Email
              </Typography>
              <Typography
                sx={{
                  fontSize: '0.875rem',
                  color: theme.palette.text.primary,
                  fontWeight: 500,
                }}
              >
                {tenant.contact?.email || 'N/A'}
              </Typography>
            </Box>

            <Box>
              <Typography
                sx={{
                  fontSize: '0.875rem',
                  color: 'body.500',
                  marginBottom: '0.5rem',
                  fontWeight: 400,
                }}
              >
                Phone number
              </Typography>
              <Typography
                sx={{
                  fontSize: '0.875rem',
                  color: theme.palette.text.primary,
                  fontWeight: 500,
                }}
              >
                {formatPhoneNumber(tenant.contact?.phoneNumber)}
              </Typography>
            </Box>
          </Box>

          {/* Second Row */}
          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: 'repeat(4, 1fr)',
              gap: '2rem',
              marginBottom: '2rem',
            }}
          >
            <Box>
              <Typography
                sx={{
                  fontSize: '0.875rem',
                  color: 'body.500',
                  marginBottom: '0.5rem',
                  fontWeight: 400,
                }}
              >
                City
              </Typography>
              <Typography
                sx={{
                  fontSize: '0.875rem',
                  color: theme.palette.text.primary,
                  fontWeight: 500,
                }}
              >
                {capitalizeWords(tenant.tenantDetails.address?.city)}
              </Typography>
            </Box>

            <Box>
              <Typography
                sx={{
                  fontSize: '0.875rem',
                  color: 'body.500',
                  marginBottom: '0.5rem',
                  fontWeight: 400,
                }}
              >
                State
              </Typography>
              <Typography
                sx={{
                  fontSize: '0.875rem',
                  color: theme.palette.text.primary,
                  fontWeight: 500,
                }}
              >
                {getFullStateName(tenant.tenantDetails.address?.state)}
              </Typography>
            </Box>
          </Box>

          {/* Separator */}
          <Box
            sx={{
              height: '1px',
              backgroundColor: theme.palette.grey[Integers.TwoHundred],
              marginBottom: '2rem',
            }}
          />

          {/* Documents Uploaded Section */}
          <Typography
            variant="h6"
            sx={{
              fontSize: '1.0rem',
              fontWeight: 600,
              color: theme.palette.text.primary,
              marginBottom: '1.5rem',
              lineHeight: 1.25,
            }}
          >
            Documents uploaded
          </Typography>

          <Box
            sx={{
              display: 'flex',
              flexWrap: 'wrap',
              gap: '1rem',
            }}
          >
            {documentsData.length > 0 ? (
              documentsData.map(doc => (
                <DocumentItem
                  key={doc.id}
                  file={
                    {
                      id: doc.id,
                      tenantId: doc.tenantId || tenantId || '',
                      fileKey: doc.fileKey,
                      originalName: doc.originalName,
                      source: doc.source || 1,
                      size: doc.size,
                      url: doc?.signedUrl || '#',
                    } as IFile
                  }
                  onClick={handleDocumentClick}
                />
              ))
            ) : (
              <Typography
                sx={{
                  fontSize: '0.875rem',
                  color: 'body.500',
                  fontStyle: 'italic',
                }}
              >
                No documents uploaded
              </Typography>
            )}
          </Box>
        </Box>
      </Box>

      {/* Close Button */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'flex-end',
          marginTop: '2rem',
        }}
      >
        <CloseButton onClick={handleClose} />
      </Box>
    </Container>
  );
};

export default TenantDetailsPage;
