import {ThemeProvider, createTheme} from '@mui/material/styles';
import {configureStore} from '@reduxjs/toolkit';
import {fireEvent, render, screen} from '@testing-library/react';
import React from 'react';
import {Provider} from 'react-redux';
import {MemoryRouter, Route, Routes} from 'react-router-dom';
import {vi} from 'vitest';
import TenantDetailsPage from './TenantDetailsPage';

// Robust theme mock for MUI useTheme
const themeMock = createTheme({
  palette: {
    white: {main: '#fff'},
    primary: {main: '#1976d2'},
    error: {main: '#d32f2f'},
    warning: {main: '#ed6c02'},
    info: {main: '#0288d1'},
    success: {main: '#2e7d32'},
    grey: {50: '#f9fafb', 200: '#e5e7eb', 300: '#d1d5db', 400: '#9ca3af', 500: '#9e9e9e'},
    body: {900: '#222', 800: '#333', 700: '#444', 500: '#888', 200: '#bbb', dark: '#000'},
    common: {black: '#000', white: '#fff'},
    background: {default: '#fafafa', paper: '#fff'},
    text: {primary: '#111827', secondary: '#6b7280'},
    divider: '#e0e0e0',
  },
});
vi.mock('@mui/material/styles', async importOriginal => ({
  ...(await importOriginal()),
  useTheme: () => themeMock,
}));
vi.mock('@mui/material', async importOriginal => ({
  ...(await importOriginal()),
  useTheme: () => themeMock,
}));
vi.mock('src/config/theme', () => ({
  default: themeMock,
}));
vi.mock('src/Providers/theme/default', () => ({
  default: themeMock,
}));

// Mock the API slice
const mockUseGetTenantByIdQuery = vi.fn();
vi.mock('redux/app/tenantManagementApiSlice', () => ({
  useGetTenantByIdQuery: () => mockUseGetTenantByIdQuery(),
}));

// Mock the components
vi.mock('Components/Breadcrumb/Breadcrumb', () => ({
  default: function MockBreadcrumb({items}: {items: any[]}) {
    return (
      <div data-testid="breadcrumb">
        {items.map(item => (
          <span key={item.href} data-testid={`breadcrumb-item-${item.label.replace(/\s+/g, '-').toLowerCase()}`}>
            {item.label}
          </span>
        ))}
      </div>
    );
  },
}));

vi.mock('../../../Components/CloseButton/CloseButton', () => ({
  default: function MockCloseButton({onClick}: {onClick: () => void}) {
    return (
      <button data-testid="close-button" onClick={onClick}>
        Close
      </button>
    );
  },
}));

vi.mock('../../../Components/DocumentItem/DocumentItem', () => ({
  default: function MockDocumentItem({file, onClick}: {file: any; onClick: (file: any) => void}) {
    return (
      <button data-testid={`document-item-${file.id}`} onClick={() => onClick(file)} type="button">
        {file.originalName}
      </button>
    );
  },
}));

vi.mock('../../../Components/SVGImageFromPath', () => ({
  default: function MockSVGImageFromPath({sx}: {sx?: any}) {
    return <div data-testid="svg-icon" style={sx} />;
  },
}));

// Mock the utility functions
vi.mock('../tenants.utils', () => ({
  getFontColor: vi.fn(() => '#000000'),
  getIndicatorColor: vi.fn(() => '#00ff00'),
  getStatusColor: vi.fn(() => '#f0f0f0'),
  getStatusLabel: vi.fn(status => {
    const statusMap: {[key: number]: string} = {
      0: 'Active',
      1: 'Pending Provision',
      2: 'Provisioning',
      3: 'Inactive',
    };
    return statusMap[status] || 'Unknown';
  }),
}));

// Create a test theme
const testTheme = themeMock;

// Create a mock store
const createMockStore = () => {
  return configureStore({
    reducer: {
      // Add minimal reducer for testing
      test: (state = {}) => state,
    },
  });
};

// Mock tenant data
const mockTenantData = {
  tenantDetails: {
    id: 'tenant-123',
    name: 'Test Tenant Company',
    status: 0, // Active
    createdOn: '2023-01-15T10:30:00Z',
    key: 'test-tenant',
    address: {
      city: 'san francisco',
      state: 'california',
    },
  },
  contact: {
    firstName: 'john',
    lastName: 'doe',
    designation: 'ceo',
    email: '<EMAIL>',
    phoneNumber: '4155551234',
  },
  files: [
    {
      id: 'file-1',
      tenantId: 'tenant-123',
      fileKey: 'documents/contract.pdf',
      originalName: 'Contract Agreement.pdf',
      source: 1,
      size: 1024000,
      signedUrl: 'https://example.com/contract.pdf',
    },
    {
      id: 'file-2',
      tenantId: 'tenant-123',
      fileKey: 'documents/terms.pdf',
      originalName: 'Terms of Service.pdf',
      source: 1,
      size: 512000,
      signedUrl: 'https://example.com/terms.pdf',
    },
  ],
};

const mockTenantDataMinimal = {
  tenantDetails: {
    id: 'tenant-456',
    name: 'Minimal Tenant',
    status: 1,
    createdOn: '2023-02-01T08:00:00Z',
    key: 'minimal-tenant',
    address: {},
  },
  contact: {},
  files: [],
};

// Helper function to render component with all providers
const renderWithProviders = (
  component: React.ReactElement,
  {initialEntries = ['/tenant/tenant-123'], store = createMockStore()} = {},
) => {
  return render(
    <Provider store={store}>
      <ThemeProvider theme={testTheme}>
        <MemoryRouter initialEntries={initialEntries}>
          <Routes>
            <Route path="/tenant/:id" element={component} />
            <Route path="/tenants" element={<div data-testid="tenants-page">Tenants Page</div>} />
          </Routes>
        </MemoryRouter>
      </ThemeProvider>
    </Provider>,
  );
};

describe('TenantDetailsPage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Mock window.open
    vi.stubGlobal('window', {
      ...window,
      open: vi.fn(),
    });
  });

  afterEach(() => {
    vi.unstubAllGlobals();
  });

  describe('Loading States', () => {
    it('displays loading message when data is loading', () => {
      mockUseGetTenantByIdQuery.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: undefined,
      });

      renderWithProviders(<TenantDetailsPage />);

      expect(screen.getByText('Loading tenant details...')).toBeInTheDocument();
    });

    it('displays error message when there is an error', () => {
      mockUseGetTenantByIdQuery.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: {message: 'Network error'},
      });

      renderWithProviders(<TenantDetailsPage />);

      expect(screen.getByText('Error loading tenant details. Please try again.')).toBeInTheDocument();
    });

    it('displays not found message when tenant data is null', () => {
      mockUseGetTenantByIdQuery.mockReturnValue({
        data: null,
        isLoading: false,
        error: undefined,
      });

      renderWithProviders(<TenantDetailsPage />);

      expect(screen.getByText('Tenant not found.')).toBeInTheDocument();
    });
  });

  describe('Successful Data Loading', () => {
    beforeEach(() => {
      mockUseGetTenantByIdQuery.mockReturnValue({
        data: mockTenantData,
        isLoading: false,
        error: undefined,
      });
    });

    it('renders tenant name and status', () => {
      renderWithProviders(<TenantDetailsPage />);

      expect(screen.getByText('Test Tenant Company')).toBeInTheDocument();
      const statusChips = screen.getAllByText('Active');
      expect(statusChips).toHaveLength(2); // One in header, one in plan details
    });

    it('renders breadcrumb with correct items', () => {
      renderWithProviders(<TenantDetailsPage />);

      expect(screen.getByTestId('breadcrumb')).toBeInTheDocument();
      expect(screen.getByTestId('breadcrumb-item-tenants')).toHaveTextContent('Tenants');
      expect(screen.getByTestId('breadcrumb-item-tenant-details')).toHaveTextContent('Tenant Details');
    });

    it('renders action buttons', () => {
      renderWithProviders(<TenantDetailsPage />);

      expect(screen.getByRole('button', {name: /edit/i})).toBeInTheDocument();
      expect(screen.getByRole('button', {name: /observability/i})).toBeInTheDocument();
      expect(screen.getByTestId('close-button')).toBeInTheDocument();
    });

    it('renders tenant information section', () => {
      renderWithProviders(<TenantDetailsPage />);

      expect(screen.getByText('Tenant information')).toBeInTheDocument();
      expect(screen.getByText('Created date')).toBeInTheDocument();
      expect(screen.getByText('Billing date')).toBeInTheDocument();
      expect(screen.getByText('Subdomain')).toBeInTheDocument();
      expect(screen.getByText('test-tenant')).toBeInTheDocument();
    });

    it('renders plan details section', () => {
      renderWithProviders(<TenantDetailsPage />);

      expect(screen.getByText('Plan details')).toBeInTheDocument();
      expect(screen.getByText('Plan name')).toBeInTheDocument();
      expect(screen.getByText('Billing Cycle')).toBeInTheDocument();
      expect(screen.getByText('Pricing')).toBeInTheDocument();
    });

    it('renders contact information section', () => {
      renderWithProviders(<TenantDetailsPage />);

      expect(screen.getByText('Contact information')).toBeInTheDocument();
      expect(screen.getByText('Name')).toBeInTheDocument();
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Job title')).toBeInTheDocument();
      expect(screen.getByText('Ceo')).toBeInTheDocument();
      expect(screen.getByText('Email')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByText('Phone number')).toBeInTheDocument();
      expect(screen.getByText('(415)555-1234')).toBeInTheDocument();
      expect(screen.getByText('City')).toBeInTheDocument();
      expect(screen.getByText('San Francisco')).toBeInTheDocument();
      expect(screen.getByText('State')).toBeInTheDocument();
      expect(screen.getByText('california')).toBeInTheDocument();
    });

    it('renders documents section with files', () => {
      renderWithProviders(<TenantDetailsPage />);

      expect(screen.getByText('Documents uploaded')).toBeInTheDocument();
      expect(screen.getByTestId('document-item-file-1')).toBeInTheDocument();
      expect(screen.getByTestId('document-item-file-2')).toBeInTheDocument();
      expect(screen.getByText('Contract Agreement.pdf')).toBeInTheDocument();
      expect(screen.getByText('Terms of Service.pdf')).toBeInTheDocument();
    });
  });

  describe('Data Formatting', () => {
    beforeEach(() => {
      mockUseGetTenantByIdQuery.mockReturnValue({
        data: mockTenantData,
        isLoading: false,
        error: undefined,
      });
    });

    it('formats date correctly', () => {
      renderWithProviders(<TenantDetailsPage />);

      expect(screen.getByText('Jan 15, 2023')).toBeInTheDocument();
    });

    it('formats phone number correctly', () => {
      renderWithProviders(<TenantDetailsPage />);

      expect(screen.getByText('(415)555-1234')).toBeInTheDocument();
    });

    it('capitalizes names correctly', () => {
      renderWithProviders(<TenantDetailsPage />);

      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('San Francisco')).toBeInTheDocument();
      expect(screen.getByText('california')).toBeInTheDocument();
    });

    it('formats subdomain correctly', () => {
      renderWithProviders(<TenantDetailsPage />);

      expect(screen.getByText('test-tenant')).toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    beforeEach(() => {
      mockUseGetTenantByIdQuery.mockReturnValue({
        data: mockTenantData,
        isLoading: false,
        error: undefined,
      });
    });

    it('handles close button click and navigates to tenants page', async () => {
      const {rerender} = renderWithProviders(<TenantDetailsPage />);

      const closeButton = screen.getByTestId('close-button');
      fireEvent.click(closeButton);

      // Re-render with tenants route to simulate navigation
      rerender(
        <Provider store={createMockStore()}>
          <ThemeProvider theme={testTheme}>
            <MemoryRouter initialEntries={['/tenants']}>
              <Routes>
                <Route path="/tenants" element={<div data-testid="tenants-page">Tenants Page</div>} />
              </Routes>
            </MemoryRouter>
          </ThemeProvider>
        </Provider>,
      );

      expect(screen.getByTestId('tenants-page')).toBeInTheDocument();
    });

    it('handles document click and opens file in new tab', () => {
      const mockWindowOpen = vi.fn();
      vi.stubGlobal('window', {...window, open: mockWindowOpen});

      renderWithProviders(<TenantDetailsPage />);

      const documentItem = screen.getByTestId('document-item-file-1');
      fireEvent.click(documentItem);

      expect(mockWindowOpen).toHaveBeenCalledWith('https://example.com/contract.pdf', '_blank', 'noopener,noreferrer');
    });

    it('handles document click with fileKey fallback', () => {
      const mockWindowOpen = vi.fn();
      vi.stubGlobal('window', {...window, open: mockWindowOpen});

      const dataWithoutSignedUrl = {
        ...mockTenantData,
        files: [
          {
            id: 'file-3',
            tenantId: 'tenant-123',
            fileKey: 'documents/backup.pdf',
            originalName: 'Backup Document.pdf',
            source: 1,
            size: 1024000,
            signedUrl: null,
          },
        ],
      };

      mockUseGetTenantByIdQuery.mockReturnValue({
        data: dataWithoutSignedUrl,
        isLoading: false,
        error: undefined,
      });

      renderWithProviders(<TenantDetailsPage />);

      const documentItem = screen.getByTestId('document-item-file-3');
      fireEvent.click(documentItem);

      expect(mockWindowOpen).toHaveBeenCalledWith('/api/files/documents/backup.pdf', '_blank', 'noopener,noreferrer');
    });
  });

  describe('Edge Cases and Minimal Data', () => {
    it('handles minimal tenant data gracefully', () => {
      mockUseGetTenantByIdQuery.mockReturnValue({
        data: mockTenantDataMinimal,
        isLoading: false,
        error: undefined,
      });

      renderWithProviders(<TenantDetailsPage />);

      expect(screen.getByText('Minimal Tenant')).toBeInTheDocument();
      const statusChips = screen.getAllByText('Pending Provision');
      expect(statusChips).toHaveLength(2); // One in header, one in plan details
      expect(screen.getAllByText('N/A')).toHaveLength(9); // Multiple N/A values for missing data
    });

    it('handles missing tenant name', () => {
      const dataWithoutName = {
        ...mockTenantData,
        tenantDetails: {
          ...mockTenantData.tenantDetails,
          name: undefined,
        },
      };

      mockUseGetTenantByIdQuery.mockReturnValue({
        data: dataWithoutName,
        isLoading: false,
        error: undefined,
      });

      renderWithProviders(<TenantDetailsPage />);

      expect(screen.getByText('Unnamed Tenant')).toBeInTheDocument();
    });

    it('displays no documents message when files array is empty', () => {
      mockUseGetTenantByIdQuery.mockReturnValue({
        data: mockTenantDataMinimal,
        isLoading: false,
        error: undefined,
      });

      renderWithProviders(<TenantDetailsPage />);

      expect(screen.getByText('No documents uploaded')).toBeInTheDocument();
    });

    it('handles invalid date gracefully', () => {
      const dataWithInvalidDate = {
        ...mockTenantData,
        tenantDetails: {
          ...mockTenantData.tenantDetails,
          createdOn: 'invalid-date',
        },
      };

      mockUseGetTenantByIdQuery.mockReturnValue({
        data: dataWithInvalidDate,
        isLoading: false,
        error: undefined,
      });

      renderWithProviders(<TenantDetailsPage />);

      // Should display Invalid Date for invalid date
      expect(screen.getByText('Invalid Date')).toBeInTheDocument();
    });

    it('handles invalid phone number gracefully', () => {
      const dataWithInvalidPhone = {
        ...mockTenantData,
        contact: {
          ...mockTenantData.contact,
          phoneNumber: '123', // Less than 10 digits
        },
      };

      mockUseGetTenantByIdQuery.mockReturnValue({
        data: dataWithInvalidPhone,
        isLoading: false,
        error: undefined,
      });

      renderWithProviders(<TenantDetailsPage />);

      // Should display original phone number if not 10 digits
      expect(screen.getByText('123')).toBeInTheDocument();
    });
  });

  describe('Component Structure and Styling', () => {
    beforeEach(() => {
      mockUseGetTenantByIdQuery.mockReturnValue({
        data: mockTenantData,
        isLoading: false,
        error: undefined,
      });
    });

    it('renders with proper container structure', () => {
      const {container} = renderWithProviders(<TenantDetailsPage />);

      // Check that main container exists
      const mainContainer = container.querySelector('[class*="MuiContainer-root"]');
      expect(mainContainer).toBeInTheDocument();
    });

    it('renders all required sections', () => {
      renderWithProviders(<TenantDetailsPage />);

      expect(screen.getByText('Tenant information')).toBeInTheDocument();
      expect(screen.getByText('Plan details')).toBeInTheDocument();
      expect(screen.getByText('Contact information')).toBeInTheDocument();
      expect(screen.getByText('Documents uploaded')).toBeInTheDocument();
    });

    it('renders status chips with proper styling', () => {
      renderWithProviders(<TenantDetailsPage />);

      const statusChips = screen.getAllByText('Active');
      expect(statusChips).toHaveLength(2); // One in header, one in plan details
      statusChips.forEach(chip => {
        expect(chip.closest('[class*="MuiChip-root"]')).toBeInTheDocument();
      });
    });

    it('renders SVG icons in buttons', () => {
      renderWithProviders(<TenantDetailsPage />);

      const svgIcons = screen.getAllByTestId('svg-icon');
      expect(svgIcons.length).toBeGreaterThan(0);
    });
  });

  describe('Accessibility', () => {
    beforeEach(() => {
      mockUseGetTenantByIdQuery.mockReturnValue({
        data: mockTenantData,
        isLoading: false,
        error: undefined,
      });
    });

    it('has proper heading structure', () => {
      renderWithProviders(<TenantDetailsPage />);

      // Main tenant name should be h4
      expect(screen.getByRole('heading', {level: 4})).toHaveTextContent('Test Tenant Company');

      // Section headings should be h6
      const sectionHeadings = screen.getAllByRole('heading', {level: 6});
      expect(sectionHeadings).toHaveLength(4);
      expect(sectionHeadings[0]).toHaveTextContent('Tenant information');
      expect(sectionHeadings[1]).toHaveTextContent('Plan details');
      expect(sectionHeadings[2]).toHaveTextContent('Contact information');
      expect(sectionHeadings[3]).toHaveTextContent('Documents uploaded');
    });

    it('has clickable buttons with proper roles', () => {
      renderWithProviders(<TenantDetailsPage />);

      const editButton = screen.getByRole('button', {name: /edit/i});
      const observabilityButton = screen.getByRole('button', {name: /observability/i});
      const closeButton = screen.getByTestId('close-button');

      expect(editButton).toBeInTheDocument();
      expect(observabilityButton).toBeInTheDocument();
      expect(closeButton).toBeInTheDocument();
    });

    it('provides meaningful text content for screen readers', () => {
      renderWithProviders(<TenantDetailsPage />);

      // Important information should be visible
      expect(screen.getByText('Test Tenant Company')).toBeVisible();
      expect(screen.getByText('<EMAIL>')).toBeVisible();
      expect(screen.getByText('(415)555-1234')).toBeVisible();
    });
  });

  describe('Route Parameters', () => {
    it('uses correct tenant ID from URL parameters', () => {
      renderWithProviders(<TenantDetailsPage />, {
        initialEntries: ['/tenant/custom-tenant-id'],
      });

      // Verify that the hook was called (the mock should be called)
      expect(mockUseGetTenantByIdQuery).toHaveBeenCalled();
    });

    it('handles missing ID parameter gracefully', () => {
      // Clear previous mock calls
      mockUseGetTenantByIdQuery.mockClear();

      // Set up mock to return data even without proper ID
      mockUseGetTenantByIdQuery.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: undefined,
      });

      renderWithProviders(<TenantDetailsPage />, {
        initialEntries: ['/tenant/undefined'],
      });

      // Should still call the hook even with undefined ID
      expect(mockUseGetTenantByIdQuery).toHaveBeenCalled();
    });
  });
});
