import Grid from '@mui/material/Grid';
import BlueButton from 'Components/BlueButton/BlueButton';
import Button from 'Components/Button';
import {FormikValues, useFormikContext} from 'formik';
import {steps} from './addTenantsUtils';

interface ButtonRenderProps {
  handleBack: () => void;
  handleNext: () => void;
  handleCancel: () => void;
  activeStep: number;
  nextButtonState?: boolean;
  isAddTenantLoader?: boolean;
  isFileUploaded?: boolean;
  isEdit?: boolean;
}

const RenderButton: React.FC<ButtonRenderProps> = props => {
  const {isValid, dirty, handleSubmit, values} = useFormikContext<FormikValues>();
  const {
    handleBack,
    handleNext,
    handleCancel,
    activeStep,
    nextButtonState = false,
    isFileUploaded = false,
    isAddTenantLoader = false,
    isEdit = false,
  } = props;

  const stepsLength = steps.length;

  const areRequiredFieldsFilled = (): boolean => {
    if (activeStep !== 0) return true;
    const requiredFields = ['company', 'key', 'firstName', 'lastName', 'email', 'city', 'state'];
    return requiredFields.every(field => values[field]?.toString().trim());
  };

  let shouldDisableNext = isEdit
    ? nextButtonState
    : !dirty || !isValid || nextButtonState || !areRequiredFieldsFilled();

  if (activeStep === 1) {
    const noPlanSelected = !values?.overAllPlan?.id;
    shouldDisableNext = shouldDisableNext || noPlanSelected;
  }

  const getBackButtonText = (): string => {
    if (activeStep === stepsLength - 2) return '< Tenant Details';
    if (activeStep === stepsLength - 1) return '< Plan Details';
    return 'Back';
  };

  const getActionButtonText = (): string => {
    if (activeStep === 0) return 'Plan Details >';
    if (activeStep === stepsLength - 1) return isEdit ? 'Update Tenant' : 'Add Tenant';
    if (activeStep === 1) return 'Upload Documents >';
    return 'Next';
  };

  return (
    <Grid
      container
      spacing={2}
      sx={{
        height: '5.125rem',
        padding: '1rem',
        opacity: 1,
        transform: 'rotate(0deg)',
        gap: '0.625rem',
        alignItems: 'center',
        justifyContent: 'space-between',
        margin: '0 auto',
      }}
    >
      <Grid size={{xs: 12, sm: 6}}>
        {activeStep !== 0 && (
          <Button
            data-testid="back-button"
            variant="outlined"
            size="large"
            onClick={handleBack}
            disabled={isAddTenantLoader}
            sx={{
              width: '12.5rem',
              borderColor: 'body.200',
              color: 'black.main',
              fontSize: '1rem',
            }}
          >
            {getBackButtonText()}
          </Button>
        )}
      </Grid>

      <Grid
        size={{xs: 12, sm: 6}} // Adjusted to use Grid's size prop for responsiveness
        sx={{
          display: 'flex',
          justifyContent: 'flex-end',
          gap: '0.625rem',
        }}
      >
        <Button
          data-testid="cancel-button"
          variant="outlined"
          name="cancel"
          onClick={handleCancel}
          disabled={isAddTenantLoader}
          sx={{
            width: '6.25rem',
            borderColor: 'body.200',
            color: 'body.dark',
            fontSize: '1rem',
            height: '3.125rem',
          }}
        >
          Cancel
        </Button>

        {activeStep === stepsLength - 1 ? (
          <BlueButton
            data-testid="submit-button"
            variant="contained"
            onClick={() => handleSubmit()}
            isLoading={isAddTenantLoader}
            size="large"
            sx={{
              width: '9.375rem',
              height: '3.125rem',
              fontSize: '1rem',
            }}
          >
            {getActionButtonText()}
          </BlueButton>
        ) : (
          <BlueButton
            data-testid="next-button"
            variant="contained"
            color="primary"
            onClick={handleNext}
            disabled={shouldDisableNext}
            sx={{
              width: '12.5rem',
              height: '3.125rem',
              fontSize: '1rem',
            }}
          >
            {getActionButtonText()}
          </BlueButton>
        )}
      </Grid>
    </Grid>
  );
};

export default RenderButton;
