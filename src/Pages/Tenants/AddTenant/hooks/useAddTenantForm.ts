import {FetchBaseQueryError} from '@reduxjs/toolkit/query';
import {FormActions} from 'Components/Forms/Form/FormUtils';
import {getErrorMessage} from 'Helpers/utils';
import {useState} from 'react';
import {useCreateTenantMutation} from 'redux/app/tenantManagementApiSlice';
import {FormAddTenant, initialAddTenantValues, TenantCreationStepType} from '../addTenantsUtils';

interface UseAddTenantFormProps {
  files: File[];
  setDialogOpen: (open: boolean) => void;
  setActiveStep: (step: TenantCreationStepType) => void;
  setCreatedTenant: (name: string | undefined) => void;
}

interface UseAddTenantFormReturn {
  initialValues: FormAddTenant;
  formSubmit: (values: FormAddTenant, actions: FormActions<FormAddTenant>) => Promise<void>;
  isLoading: boolean;
}

export const useAddTenantForm = ({
  files,
  setDialogOpen,
  setActiveStep,
  setCreatedTenant,
}: UseAddTenantFormProps): UseAddTenantFormReturn => {
  const [initialValues] = useState<FormAddTenant>(initialAddTenantValues);

  const [createTenant, {isLoading}] = useCreateTenantMutation();
  const formSubmit = async (values: FormAddTenant, actions: FormActions<FormAddTenant>): Promise<void> => {
    const formData = createFormData(values, files);

    try {
      await createTenant(formData).unwrap();
      setCreatedTenant(values.company);
      actions.resetForm({values: initialValues});
      setDialogOpen(true);
    } catch (error) {
      handleFormError(error, actions, setActiveStep);
    }
  };

  return {
    initialValues,
    formSubmit,
    isLoading,
  };
};

const createFormData = (values: FormAddTenant, files: File[]): FormData => {
  const formData = new FormData();
  formData.append('name', values.company.trim());
  formData.append('lang', values.language.trim());

  if (values.overAllPlan) {
    formData.append('planId', values.overAllPlan.id);
    if (values.userCount !== undefined && !values.overAllPlan.allowedUnlimitedUsers) {
      formData.append('numberOfUsers', values.userCount.toString());
    }
    if (values.totalCost !== undefined) {
      formData.append('totalCost', values.totalCost.toString());
    }
  }

  formData.append(
    'contact',
    JSON.stringify({
      firstName: values.firstName.trim(),
      lastName: values.lastName.trim(),
      isPrimary: true,
      email: values.email.trim(),
      phoneNumber: values.mobileNumber?.trim(),
      countryCode: values.countryCode.code.trim(),
      designation: values.designation?.trim(),
    }),
  );

  files.forEach(file => {
    formData.append('files', file);
  });

  const country = 'USA';
  formData.append('key', values.key.trim());
  formData.append('city', values.city.trim());
  formData.append('state', values.state.trim());
  formData.append('country', country);
  return formData;
};

const handleFormError = (
  error: unknown,
  actions: FormActions<FormAddTenant>,
  setActiveStep: (step: TenantCreationStepType) => void,
): void => {
  const errorMessage = getErrorMessage(error as FetchBaseQueryError);

  if (errorMessage.includes('Subdomain')) {
    setActiveStep(0);
    actions.setErrors({key: errorMessage});
  } else if (errorMessage.includes('email')) {
    setActiveStep(0);
    actions.setErrors({email: errorMessage});
  } else if (errorMessage.includes('Duplicate feature')) {
    setActiveStep(1);
  } else {
    //DO NOTHING
  }
};
