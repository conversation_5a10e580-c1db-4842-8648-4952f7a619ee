import UploadDocuments from 'Components/UploadDocument';
import {MAX_FILES, MAX_FILE_SIZE} from 'Helpers/utils';
import {useMemo} from 'react';
import {PlanResponse} from 'redux/app/types/plan.type';
import AddTenantDetails from '../AddTenantDetail';
import {TenantCreationStepType} from '../addTenantsUtils';
import ChoosePlan from '../PlanSection/ChoosePlan';

interface UseAddTenantStepsProps {
  handleNextButton: (state: boolean) => void;
  setOverAllPlan: (plan: PlanResponse | null) => void;
  files: File[];
  onFileUpload: (files: File[]) => void;
  onRemoveFile: (files: File[]) => void;
  overAllPlan?: PlanResponse | null;
  userCount: number;
  setUserCount: (count: number) => void;
  selectedDevice?: string;
  setSelectedDevice: (val: string) => void;
  selectedTenure?: string;
  setSelectedTenure: (val: string) => void;
  selectedInfraConfig?: string;
  setSelectedInfraConfig: (val: string) => void;
}

export const useAddTenantSteps = (props: UseAddTenantStepsProps) => {
  const stepComponents = useMemo<Record<TenantCreationStepType, React.ReactNode>>(
    () => ({
      0: <AddTenantDetails />,
      1: (
        <ChoosePlan
          handleNextButton={props.handleNextButton}
          setOverAllPlan={props.setOverAllPlan}
          overAllPlan={props.overAllPlan}
          userCount={props.userCount}
          setUserCount={props.setUserCount}
          selectedDevice={props.selectedDevice}
          setSelectedDevice={props.setSelectedDevice}
          selectedTenure={props.selectedTenure}
          setSelectedTenure={props.setSelectedTenure}
          selectedInfraConfig={props.selectedInfraConfig}
          setSelectedInfraConfig={props.setSelectedInfraConfig}
        />
      ),
      2: (
        <UploadDocuments
          primaryText="0 Files attached"
          onUpload={props.onFileUpload}
          onRemoveFile={props.onRemoveFile}
          files={props.files}
          dropzoneProps={{maxFiles: MAX_FILES, maxSize: MAX_FILE_SIZE, multiple: true}}
        />
      ),
    }),
    [props],
  );

  const renderStepContent = (step: TenantCreationStepType) => stepComponents[step] || null;

  return {
    stepComponents,
    renderStepContent,
  };
};
