import {PlanResponse} from 'redux/app/types/plan.type';
import * as yup from 'yup';
export const steps = ['Tenant Details', 'Plan Details', 'Documents'];

export const countryCodes = [{code: '+1', label: 'USA'}];

export type PlanSelectedType = {
  planId: string;
  name?: string;
  duration?: string;
  amount?: string | number;
  billingCycleId?: string;
};

export interface CountryCode {
  code: string;
  label: string;
}

// US States array for the state dropdown
export const usStates = [
  {value: 'AL', label: 'Alabama'},
  {value: 'AK', label: 'Alaska'},
  {value: 'AZ', label: 'Arizona'},
  {value: 'AR', label: 'Arkansas'},
  {value: 'CA', label: 'California'},
  {value: 'CO', label: 'Colorado'},
  {value: 'CT', label: 'Connecticut'},
  {value: 'DE', label: 'Delaware'},
  {value: 'FL', label: 'Florida'},
  {value: 'GA', label: 'Georgia'},
  {value: 'HI', label: 'Hawaii'},
  {value: 'ID', label: 'Idaho'},
  {value: 'IL', label: 'Illinois'},
  {value: 'IN', label: 'Indiana'},
  {value: 'IA', label: 'Iowa'},
  {value: 'KS', label: 'Kansas'},
  {value: 'KY', label: 'Kentucky'},
  {value: 'LA', label: 'Louisiana'},
  {value: 'ME', label: 'Maine'},
  {value: 'MD', label: 'Maryland'},
  {value: 'MA', label: 'Massachusetts'},
  {value: 'MI', label: 'Michigan'},
  {value: 'MN', label: 'Minnesota'},
  {value: 'MS', label: 'Mississippi'},
  {value: 'MO', label: 'Missouri'},
  {value: 'MT', label: 'Montana'},
  {value: 'NE', label: 'Nebraska'},
  {value: 'NV', label: 'Nevada'},
  {value: 'NH', label: 'New Hampshire'},
  {value: 'NJ', label: 'New Jersey'},
  {value: 'NM', label: 'New Mexico'},
  {value: 'NY', label: 'New York'},
  {value: 'NC', label: 'North Carolina'},
  {value: 'ND', label: 'North Dakota'},
  {value: 'OH', label: 'Ohio'},
  {value: 'OK', label: 'Oklahoma'},
  {value: 'OR', label: 'Oregon'},
  {value: 'PA', label: 'Pennsylvania'},
  {value: 'RI', label: 'Rhode Island'},
  {value: 'SC', label: 'South Carolina'},
  {value: 'SD', label: 'South Dakota'},
  {value: 'TN', label: 'Tennessee'},
  {value: 'TX', label: 'Texas'},
  {value: 'UT', label: 'Utah'},
  {value: 'VT', label: 'Vermont'},
  {value: 'VA', label: 'Virginia'},
  {value: 'WA', label: 'Washington'},
  {value: 'WV', label: 'West Virginia'},
  {value: 'WI', label: 'Wisconsin'},
  {value: 'WY', label: 'Wyoming'},
];

// Extract valid state values for validation
const validStateValues = usStates.map(state => state.value);
// Regex constants
const COMPANY_NAME_REGEX = /^(?![-\s])[a-zA-Z0-9\s&.,'’-]+(?<![-\s])$/;
const SUBDOMAIN_REGEX = /^(?!-)[a-zA-Z0-9-]{3,63}(?<!-)$/;
const NAME_REGEX = /^[a-zA-Z\s]+$/;
const EMAIL_REGEX = /^[a-zA-Z0-9._%+-]+@([a-zA-Z0-9-]+\.){1,3}[a-zA-Z]{2,7}$/;
const CITY_REGEX = /^[A-Za-z ]+$/;
const MOBILE_NUMBER_REGEX = /^\d{10}$/;
// Constants
const COMPANY_NAME_MIN = 3;
const COMPANY_NAME_MAX = 50;

const SUBDOMAIN_MIN = 3;
const SUBDOMAIN_MAX = 63;

const NAME_MIN = 3;
const NAME_MAX = 50;

const DESIGNATION_MIN = 3;
const DESIGNATION_MAX = 50;

const EMAIL_MAX = 50;

const CITY_MIN = 2;
const CITY_MAX = 50;

const MOBILE_NUMBER_LENGTH = 10;
const noWhiteSpace = 'not-only-whitespace';

export const addTenantValidationSchema = yup.object({
  company: yup
    .string()
    .required('Company is required')
    .min(COMPANY_NAME_MIN, `Company should have at least ${COMPANY_NAME_MIN} characters`)
    .max(COMPANY_NAME_MAX, `Company should have at most ${COMPANY_NAME_MAX} characters`)
    .matches(
      COMPANY_NAME_REGEX,
      'Company name should only contain letters, numbers, spaces, and valid punctuation in between.',
    )
    .test(noWhiteSpace, 'Company cannot be only whitespace', value => !!value?.trim()),

  key: yup
    .string()
    .required('Subdomain is required')
    .min(SUBDOMAIN_MIN, `Minimum ${SUBDOMAIN_MIN} characters required`)
    .max(SUBDOMAIN_MAX, `Maximum ${SUBDOMAIN_MAX} characters allowed`)
    .matches(SUBDOMAIN_REGEX, 'Subdomain should only contain lowercase letters, numbers, and hyphen in between.')
    .test(noWhiteSpace, 'Subdomain cannot be only whitespace', value => !!value?.trim()),

  firstName: yup
    .string()
    .required('First Name is required')
    .min(NAME_MIN, `First Name should have at least ${NAME_MIN} characters`)
    .max(NAME_MAX, `First Name should have at most ${NAME_MAX} characters`)
    .matches(NAME_REGEX, 'First Name should only contain letters')
    .test(noWhiteSpace, 'First Name cannot be only whitespace', value => !!value?.trim()),

  lastName: yup
    .string()
    .required('Last Name is required')
    .min(NAME_MIN, `Last Name should have at least ${NAME_MIN} characters`)
    .max(NAME_MAX, `Last Name should have at most ${NAME_MAX} characters`)
    .matches(NAME_REGEX, 'Last Name should only contain letters')
    .test(noWhiteSpace, 'Last Name cannot be only whitespace', value => !!value?.trim()),

  designation: yup
    .string()
    .min(DESIGNATION_MIN, `Designation should have at least ${DESIGNATION_MIN} characters`)
    .max(DESIGNATION_MAX, `Designation should have at most ${DESIGNATION_MAX} characters`)
    .matches(NAME_REGEX, 'Designation should only contain letters')
    .test(noWhiteSpace, 'Designation cannot be only whitespace', value => value === undefined || !!value.trim()),

  email: yup
    .string()
    .required('Email is required')
    .email('Invalid email address')
    .max(EMAIL_MAX, `Email should have at most ${EMAIL_MAX} characters`)
    .matches(EMAIL_REGEX, 'Invalid email address')
    .test(noWhiteSpace, 'Email cannot be only whitespace', value => !!value?.trim()),

  countryCode: yup
    .object()
    .shape({
      value: yup.string(),
      label: yup.string(),
    })
    .required('Country Code is required'),

  city: yup
    .string()
    .required('City is required')
    .matches(CITY_REGEX, 'City can only contain letters and spaces')
    .min(CITY_MIN, `City must be at least ${CITY_MIN} characters`)
    .max(CITY_MAX, `City cannot exceed ${CITY_MAX} characters`)
    .test(noWhiteSpace, 'City cannot be only whitespace', value => !!value?.trim()),

  mobileNumber: yup
    .string()
    .matches(MOBILE_NUMBER_REGEX, `Mobile Number must be exactly ${MOBILE_NUMBER_LENGTH} digits`)
    .test(noWhiteSpace, 'Mobile Number cannot be only whitespace', value => value === undefined || !!value.trim()),

  state: yup
    .string()
    .required('State is required')
    .oneOf(validStateValues, 'Please select a valid state')
    .test('is-valid-state', 'Please select a valid state from the list', value => {
      if (!value) return false;
      return validStateValues.includes(value.trim());
    }),
});

export interface FormAddTenant {
  firstName: string;
  lastName: string;
  company: string;
  designation: string | undefined;
  email: string;
  countryCode: CountryCode;
  mobileNumber: string | undefined;
  language: string;
  overAllPlan?: PlanResponse;
  userCount?: number;
  totalCost?: number;
  files: File[];
  key: string;
  city: string;
  state: string;
}

export const initialAddTenantValues: FormAddTenant = {
  firstName: '',
  lastName: '',
  company: '',
  designation: undefined,
  email: '',
  key: '',
  countryCode: {code: '+1', label: 'USA'},
  mobileNumber: undefined,
  language: 'English',
  files: [],
  overAllPlan: undefined,
  userCount: 0,
  totalCost: 0,
  city: '',
  state: '',
};

export enum TenantCreationStepType {
  TenantDetails = 0,
  PlanDetails = 1,
  Documents = 2,
}
