/**
 * This component renders the tenant detail form used for onboarding or editing a tenant.
 * It includes validation for subdomain availability and suggestions if the subdomain is taken.
 *
 * Features:
 * - Debounced subdomain availability check
 * - Dynamic adornment icons
 * - Read-only toggle for edit mode
 * - Company and contact detail input fields
 */

import CancelOutlinedIcon from '@mui/icons-material/CancelOutlined';
import {
  Box,
  CircularProgress,
  Grid,
  InputAdornment,
  List,
  ListItem,
  ListItemButton,
  MenuItem,
  Select,
  Typography,
} from '@mui/material';
import FormInput from 'Components/Forms/FormInput';
import FormSelect from 'Components/Forms/FormSelect/FormSelect';
import {REGISTERED_DOMAIN} from 'Constants/enums';
import {useFormikContext} from 'formik';
import {Integers} from 'Helpers/integers';
import {StyleUtils} from 'Helpers/styleUtils';
import {debounce} from 'lodash';
import {FC, useCallback, useEffect, useState} from 'react';
import {useVerifyTenantKeyMutation} from 'redux/app/tenantManagementApiSlice';
import {AnyObject} from 'yup';
import Icon from '../../../Assets/Icons.svg';
import {countryCodes, FormAddTenant, steps, usStates} from './addTenantsUtils';

const alertSuccessMain = 'alert.success.main ';
/**
 * API response type for verifying tenant key availability
 */
export type VerifyTenantKeyResponse = {
  available: boolean;
  suggestions?: string[];
};
const DEBOUNCE_DELAY_MS = 500;
const IMPORTANT = ' !important';
interface Props {
  /** When true, disables editing of fields */
  isEdit?: boolean;
}

/**
 * Returns an adornment component (spinner/icon/cancel) for subdomain input
 */
const getEndAdornmentContent = ({
  key,
  loading,
  isAvailable,
  setFieldValue,
  setIsAvailable,
  setSuggestions,
  isKeyFieldValid,
}: {
  key: string;
  loading: boolean;
  isAvailable: boolean | null;
  setFieldValue: (field: string, value: unknown, shouldValidate?: boolean) => void;
  setIsAvailable: (val: boolean | null) => void;
  setSuggestions: (val: string[]) => void;
  isKeyFieldValid?: boolean;
}) => {
  if (key.length < steps.length) return null;

  if (loading && isKeyFieldValid) {
    return <CircularProgress size={20} />;
  }

  if (isAvailable === true && isKeyFieldValid) {
    return <img src={Icon} alt="Available" style={{width: 20, height: 20, objectFit: 'contain'}} />;
  }

  if (isAvailable === false && isKeyFieldValid) {
    return (
      <CancelOutlinedIcon
        color="error"
        sx={{cursor: 'pointer'}}
        onClick={() => {
          setFieldValue('key', '');
          setIsAvailable(null);
          setSuggestions([]);
        }}
      />
    );
  }

  return null;
};

/**
 * Returns MUI styles for the subdomain input box based on availability
 */
const getSxPropsValue = (isAvailable: boolean | null, keyLength: number, isKeyFieldValid?: boolean): AnyObject => {
  if ((isAvailable === true && keyLength < steps.length) || !isKeyFieldValid) {
    return {
      '& .MuiOutlinedInput-notchedOutline': {
        borderColor: 'red',
      },
      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
        borderColor: 'body.100',
      },
    };
  }

  if (isAvailable === true) {
    return {
      '& .MuiOutlinedInput-notchedOutline': {
        borderColor: alertSuccessMain + IMPORTANT,
      },
      '&:hover .MuiOutlinedInput-notchedOutline': {
        borderColor: alertSuccessMain + IMPORTANT,
      },
      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
        borderColor: alertSuccessMain + IMPORTANT,
      },
    };
  }

  if (isAvailable === false) {
    return {
      '&:hover .MuiOutlinedInput-notchedOutline': {
        borderColor: 'alert.error.onBg ' + IMPORTANT,
      },
      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
        borderColor: 'alert.error.onBg ' + IMPORTANT,
      },
    };
  }

  return {};
};

/**
 * A form component for entering tenant company and contact information.
 * Supports both add and edit modes.
 */
const AddTenantDetail: FC<Props> = ({isEdit}) => {
  const {values, errors, touched, setFieldValue} = useFormikContext<FormAddTenant>();

  const [verifyTenantKey] = useVerifyTenantKeyMutation();
  const [isAvailable, setIsAvailable] = useState<boolean | null>(null);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  /**
   * Debounced API call to verify tenant key availability
   */
  const debouncedVerify = useCallback(
    debounce(async (key: string) => {
      if (!key.trim()) return;
      setLoading(true);
      try {
        const res: VerifyTenantKeyResponse = await verifyTenantKey({key}).unwrap();
        setIsAvailable(res.available);
        setSuggestions(res.available ? [] : res.suggestions || []);
      } catch (error) {
        console.info('Verify Key Error:', error);
        setIsAvailable(false);
      } finally {
        setLoading(false);
      }
    }, DEBOUNCE_DELAY_MS),
    [],
  );

  /**
   * Trigger subdomain check when value changes and not in edit mode
   */
  useEffect(() => {
    if (values.key && !isEdit) {
      debouncedVerify(values.key);
    }
  }, [values.key, debouncedVerify, isEdit]);

  // To check if the key field has an error AND has been touched
  const isKeyFieldValid = !errors.key;
  const sxPropsValue = getSxPropsValue(isAvailable, values.key.length, isKeyFieldValid);
  const adornmentContent = getEndAdornmentContent({
    key: values.key,
    loading,
    isAvailable,
    setFieldValue,
    setIsAvailable,
    setSuggestions,
    isKeyFieldValid,
  });

  return (
    <Grid container spacing={2} rowSpacing={2} sx={{height: '100%', boxSizing: 'border-box', padding: 2}}>
      {/* Section Header */}
      <Grid size={{xs: 12}}>
        <Typography variant="h6" fontWeight={600} sx={{fontSize: '1rem'}}>
          Tenant Information
        </Typography>
      </Grid>

      {/* Company Name */}
      <Grid size={{xs: 12, sm: 6}}>
        <Typography sx={StyleUtils.lalelStyles}>Company *</Typography>
        <FormInput
          fullWidth
          id="company"
          name="company"
          required
          sx={StyleUtils.inputBoxStyles}
          readOnly={isEdit}
          placeholder="Enter company name"
        />
      </Grid>

      {/* Subdomain Key */}
      <Grid container spacing={0} size={{xs: 12, sm: 6}} alignItems="stretch">
        <Grid size={{xs: 9}}>
          <Typography sx={StyleUtils.lalelStyles}>Subdomain *</Typography>
          <Box sx={{position: 'relative', display: 'inline-block', width: '100%'}}>
            <FormInput
              fullWidth
              id="key"
              name="key"
              required
              sx={[StyleUtils.inputBoxStyles, sxPropsValue]}
              placeholder="Enter subdomain"
              readOnly={isEdit}
              endAdornment={
                adornmentContent && (
                  <InputAdornment position="end" sx={{pr: 1}}>
                    {adornmentContent}
                  </InputAdornment>
                )
              }
            />

            {/* Subdomain suggestions */}
            {isAvailable === false && (
              <Box
                sx={{
                  border: theme => `0.0625rem solid ${theme.palette.body[Integers.OneHundred]}`,
                  mt: 1,
                  borderRadius: 1,
                  background: 'white.main',
                  position: 'absolute',
                  zIndex: 1,
                  width: '100%',
                }}
              >
                <Typography sx={{fontSize: '0.85rem', p: 1}}>
                  <Typography component="span" sx={{color: 'alert.error.main', fontSize: '0.85rem'}}>
                    <strong>"{values.key}" is already taken.</strong>
                  </Typography>
                  <br />
                  Here are some suggestions.
                </Typography>

                {suggestions.length > 0 && (
                  <Grid container justifyContent="center" alignItems="center">
                    {suggestions.map(suggestion => (
                      <Grid size={{xs: 12}} key={suggestion} sx={{display: 'flex', justifyContent: 'center'}}>
                        <List dense sx={{width: '100%', display: 'flex', justifyContent: 'center', p: 0, m: 0}}>
                          <ListItem
                            disablePadding
                            sx={{
                              backgroundColor: 'white.200',
                              borderRadius: 2,
                              width: '96%',
                              mb: 1,
                            }}
                          >
                            <ListItemButton
                              data-testid={`suggestion-${suggestion}`}
                              onClick={() => setFieldValue('key', suggestion)}
                              sx={{fontSize: '0.9rem'}}
                            >
                              {suggestion + REGISTERED_DOMAIN}
                            </ListItemButton>
                          </ListItem>
                        </List>
                      </Grid>
                    ))}
                  </Grid>
                )}
              </Box>
            )}
          </Box>
        </Grid>

        {/* Domain suffix */}
        <Grid size={{xs: 3}}>
          <Box sx={{height: '100%', display: 'flex', paddingTop: '1.2rem', alignItems: 'center', pl: 0.5}}>
            <Typography>{REGISTERED_DOMAIN}</Typography>
          </Box>
        </Grid>
      </Grid>

      {/* Contact Info Header */}
      <Grid size={{xs: 12}} sx={{mt: 2}}>
        <Typography variant="h6" fontWeight={600} sx={{fontSize: '1rem'}}>
          Contact Information
        </Typography>
      </Grid>

      {/* Contact Fields */}
      <Grid size={{xs: 12, sm: 6}}>
        <Typography sx={StyleUtils.lalelStyles}>First name *</Typography>
        <FormInput
          fullWidth
          id="firstName"
          name="firstName"
          placeholder="Enter first name"
          required
          sx={StyleUtils.inputBoxStyles}
          readOnly={isEdit}
        />
      </Grid>
      <Grid size={{xs: 12, sm: 6}}>
        <Typography sx={StyleUtils.lalelStyles}>Last name *</Typography>
        <FormInput
          fullWidth
          id="lastName"
          name="lastName"
          placeholder="Enter last name"
          required
          sx={StyleUtils.inputBoxStyles}
          readOnly={isEdit}
        />
      </Grid>
      <Grid size={{xs: 12, sm: 6}}>
        <Typography sx={StyleUtils.lalelStyles}>Job title</Typography>
        <FormInput
          fullWidth
          id="designation"
          name="designation"
          placeholder="Enter job title"
          sx={StyleUtils.inputBoxStyles}
          readOnly={isEdit}
        />
      </Grid>
      <Grid size={{xs: 12, sm: 6}}>
        <Typography sx={StyleUtils.lalelStyles}>Primary phone number</Typography>
        <FormInput
          fullWidth
          id="mobileNumber"
          name="mobileNumber"
          placeholder="Enter primary phone number"
          onInput={e => {
            const target = e.target as HTMLInputElement;
            target.value = target.value.replace(/\D/g, '');
          }}
          readOnly={isEdit}
          sxProps={{paddingLeft: 0}}
          sx={{...StyleUtils.inputBoxStyles, p: 0}}
          startAdornment={
            <InputAdornment position="start" sx={{...StyleUtils.inputAdornment}}>
              <Select
                data-testid="country-code-select"
                variant="standard"
                disableUnderline
                disabled
                defaultValue={values?.countryCode?.code}
                sx={{
                  ...StyleUtils.selectBox,
                  '& .Mui-disabled': {
                    '-webkit-text-fill-color': 'black.main',
                  },
                }}
                IconComponent={() => null}
              >
                {countryCodes.map(option => (
                  <MenuItem key={option.code} value={option.code} data-testid={`country-code-${option.code}`}>
                    {option.code}
                  </MenuItem>
                ))}
              </Select>
            </InputAdornment>
          }
        />
      </Grid>
      <Grid size={{xs: 12, sm: 6}}>
        <Typography sx={StyleUtils.lalelStyles}>Email address *</Typography>
        <FormInput
          fullWidth
          id="email"
          name="email"
          type="email"
          placeholder="Enter email address"
          required
          sx={StyleUtils.inputBoxStyles}
          readOnly={isEdit}
        />
      </Grid>
      <Grid size={{xs: 12, sm: 6}}>
        <Typography sx={StyleUtils.lalelStyles}>City *</Typography>
        <FormInput fullWidth id="city" name="city" placeholder="Enter city" required sx={StyleUtils.inputBoxStyles} />
      </Grid>
      <Grid size={{xs: 12, sm: 6}}>
        <Typography sx={StyleUtils.lalelStyles}>State *</Typography>
        <FormSelect
          fullWidth
          id="state"
          name="state"
          placeholder="Select state"
          required
          sx={StyleUtils.selectBoxStyles}
          options={usStates}
          readOnly={isEdit}
          menuPlacement="top"
        />
      </Grid>
    </Grid>
  );
};

export default AddTenantDetail;
