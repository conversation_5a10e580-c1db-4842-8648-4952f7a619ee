// @vitest-environment jsdom
import {render, screen, waitFor} from '@testing-library/react';
import {renderWithTheme} from 'TestHelper/TestHelper';
import {afterEach, beforeEach, describe, expect, it, vi} from 'vitest';

// Mock Integers enum if used
vi.mock('Helpers/integers', () => ({
  Integers: {One: 1, Two: 2},
}));

// Mock theme for useTheme in both possible import paths
const themeMock = {
  palette: {
    plan: {
      normal: {1: '#aaa', 2: '#bbb'},
      selected: {1: '#ccc', 2: '#ddd'},
    },
  },
};
vi.mock('@mui/material/styles', async original => ({
  ...(await original()),
  useTheme: () => themeMock,
}));
vi.mock('@mui/material', async original => ({
  ...(await original()),
  useTheme: () => themeMock,
}));

// Default helpers mock (function declaration for hoisting)
async function helpersDefaultMock() {
  const actual = await vi.importActual<typeof import('./choosePlanHelpers')>('./choosePlanHelpers');
  return {
    ...actual,
    shouldShowError: () => false,
    shouldShowLoader: () => false,
  };
}
vi.mock('./choosePlanHelpers', helpersDefaultMock);

// Mock all dependencies
const devicesResult = {
  data: [
    {id: 'dev1', min: '1', max: '10'},
    {id: 'dev2', min: '11', max: '50'},
  ],
  isLoading: false,
  error: null,
};
const tenuresResult = {
  data: [
    {id: 'ten1', cycleName: 'Monthly'},
    {id: 'ten2', cycleName: 'Quarterly'},
    {id: 'ten3', cycleName: 'Annually'},
  ],
  isLoading: false,
  error: null,
};
const plansResult = {
  data: [
    {
      id: 'plan1',
      name: 'Basic',
      price: '100',
      costPerUser: '10',
      allowedUnlimitedUsers: false,
      tier: 'infra1',
      currency: {
        id: 'cur1',
        symbol: '$',
        currencyCode: 'USD',
        currencyName: 'US Dollar',
        country: 'US',
      },
      configureDevice: {
        id: 'dev1',
        min: '1',
        max: '10',
        deleted: false,
        deletedOn: null,
        deletedBy: null,
        createdOn: '',
        createdBy: '',
        modifiedOn: '',
        modifiedBy: '',
        version: '1',
      },
      deleted: false,
      deletedOn: null,
      deletedBy: null,
      createdOn: '',
      createdBy: '',
      modifiedOn: '',
      modifiedBy: '',
      description: '',
      size: '0',
      status: '1',
      version: '1',
      billingCycleId: '',
      currencyId: '',
      configureDeviceId: '',
      planSizeId: '',
    },
  ],
  isLoading: false,
  error: null,
};

vi.mock('redux/app/planManagementAPiSlice', () => ({
  useLazyGetDevicesQuery: () => [vi.fn(), devicesResult],
  useLazyGetTenuresQuery: () => [vi.fn(), tenuresResult],
  useLazyGetPlansQuery: () => [vi.fn(), plansResult],
}));

let formikValues: any;
function resetFormikValues(overrides = {}) {
  formikValues = {
    billingCycle: 'ten1',
    noOfDevices: 'dev1',
    infraConfig: 'infra1',
    userCount: 2,
    overAllPlan: null,
    totalCost: 0,
    tenurePeriod: 'Monthly',
    selectedDevice: 'dev1',
    selectedTenure: 'ten1',
    selectedInfraConfig: 'infra1',
    ...overrides,
  };
}
resetFormikValues();

vi.mock('formik', () => ({
  useFormikContext: () => ({
    values: formikValues,
    setValues: (updater: any) => {
      const updated = typeof updater === 'function' ? updater(formikValues) : updater;
      Object.assign(formikValues as any, updated);
    },
    setFieldValue: (field: string, value: any) => {
      (formikValues as any)[field] = value;
    },
  }),
}));

vi.mock('Pages/PlansPage/plans.utils', async original => ({
  ...(await original()),
  capitalize: (s: string) => s.charAt(0).toUpperCase() + s.slice(1),
  groupedFeatureOptions: [
    {value: 'infra1', label: 'Infra 1', features: ['FeatureA', 'FeatureB']},
    {value: 'infra2', label: 'Infra 2', features: ['FeatureC', 'FeatureD']},
  ],
}));

vi.mock('Components/BackdropLoader/BackdropLoader', () => ({
  __esModule: true,
  default: () => <div data-testid="BackdropLoader">Loading...</div>,
}));

vi.mock('Components/Forms/FormSelect/FormSelect', () => ({
  __esModule: true,
  default: ({options, value, onChange, ...props}: any) => (
    <select data-testid={props['data-testid']} value={value} onChange={e => onChange(e.target.value)}>
      {options.map((opt: any) => (
        <option key={opt.value} value={opt.value}>
          {opt.label}
        </option>
      ))}
    </select>
  ),
}));

vi.mock('Components/Button', () => ({
  __esModule: true,
  default: (props: any) => (
    <button data-testid={props['data-testid'] || 'button'} {...props}>
      {props.children}
    </button>
  ),
}));

// Mock child components
vi.mock('./PlanCard', () => ({
  __esModule: true,
  default: ({plan, isSelected, onSelect}: any) => (
    <div data-testid="plan-card">
      <h3>{plan.name}</h3>
      <p>${plan.price}</p>
      {!isSelected && (
        <button data-testid="select-plan-btn" onClick={() => onSelect(plan.id)}>
          Select This Plan
        </button>
      )}
    </div>
  ),
}));

vi.mock('./PlanSummary', () => ({
  __esModule: true,
  default: ({selectedPlan, userCount, setUserCount}: any) => (
    <div data-testid="plan-summary">
      <h4>Plan Summary</h4>
      {!selectedPlan.allowedUnlimitedUsers && (
        <div>
          <span>Users: {userCount}</span>
          <button data-testid="decrease-user-count" onClick={() => setUserCount(Math.max(1, userCount - 1))}>
            -
          </button>
          <button data-testid="increase-user-count" onClick={() => setUserCount(userCount + 1)}>
            +
          </button>
        </div>
      )}
    </div>
  ),
}));

const defaultProps = {
  handleNextButton: vi.fn(),
  setOverAllPlan: vi.fn(),
  overAllPlan: null,
  userCount: 2,
  setUserCount: vi.fn(),
  selectedDevice: 'dev1',
  setSelectedDevice: vi.fn(),
  selectedTenure: 'Monthly',
  setSelectedTenure: vi.fn(),
  selectedInfraConfig: 'infra1',
  setSelectedInfraConfig: vi.fn(),
};

async function setup(props = {}) {
  const {default: ChoosePlan} = await import('./ChoosePlan');
  return render(<ChoosePlan {...defaultProps} {...props} />);
}

describe('ChoosePlan Component Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    resetFormikValues();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('renders without hanging', async () => {
    const {default: ChoosePlan} = await import('./ChoosePlan');
    const {container} = renderWithTheme(<ChoosePlan {...defaultProps} />);
    await waitFor(() => {
      expect(container).toBeInTheDocument();
    });
  });

  it('renders fallback for renderTier', async () => {
    const {renderTier} = await import('./ChoosePlan');
    expect(renderTier('')).toBe('-');
    expect(renderTier('nonexistent')).toBe('-');
  });

  it('renders loader when data is loading', async () => {
    vi.resetModules();
    vi.doMock('./choosePlanHelpers', async () => {
      const actual = await vi.importActual<typeof import('./choosePlanHelpers')>('./choosePlanHelpers');
      return {
        ...actual,
        shouldShowError: () => false,
        shouldShowLoader: () => true,
      };
    });
    const {default: ChoosePlan} = await import('./ChoosePlan');
    render(<ChoosePlan {...defaultProps} />);
    await waitFor(() => {
      expect(screen.getByTestId('BackdropLoader')).toBeInTheDocument();
    });
    // Reset helpers after loader test
    vi.resetModules();
    vi.doMock('./choosePlanHelpers', helpersDefaultMock);
  });

  it('renders plan cards and allows plan selection', async () => {
    resetFormikValues({
      noOfDevices: 'dev1',
      infraConfig: 'infra1',
      billingCycle: 'ten1',
      selectedDevice: 'dev1',
      selectedInfraConfig: 'infra1',
      selectedTenure: 'ten1',
    });
    const {default: ChoosePlan} = await import('./ChoosePlan');
    const setOverAllPlan = vi.fn();
    const handleNextButton = vi.fn();
    renderWithTheme(
      <ChoosePlan {...defaultProps} setOverAllPlan={setOverAllPlan} handleNextButton={handleNextButton} />,
    );
    expect(screen.getByTestId('plan-card')).toBeInTheDocument();
    expect(screen.getByText('Basic')).toBeInTheDocument();
    expect(screen.getByText('$100')).toBeInTheDocument();

    // Select plan button
    const selectBtn = screen.getByTestId('select-plan-btn');
    selectBtn.click();
    expect(setOverAllPlan).toHaveBeenCalled();
    expect(handleNextButton).toHaveBeenCalledWith(false);
  });

  it('renders plan summary and allows user count increment/decrement', async () => {
    resetFormikValues({
      noOfDevices: 'dev1',
      infraConfig: 'infra1',
      billingCycle: 'ten1',
      selectedDevice: 'dev1',
      selectedInfraConfig: 'infra1',
      selectedTenure: 'ten1',
      userCount: 2,
    });
    const {default: ChoosePlan} = await import('./ChoosePlan');
    const setUserCount = vi.fn();
    const mockPlan = {
      id: 'plan1',
      name: 'Basic',
      price: '100',
      costPerUser: '10',
      allowedUnlimitedUsers: false,
      tier: 'infra1',
      currency: {
        id: 'cur1',
        symbol: '$',
        currencyCode: 'USD',
        currencyName: 'US Dollar',
        country: 'US',
      },
      configureDevice: {
        id: 'dev1',
        min: 1,
        max: 10,
        deleted: false,
        deletedOn: null,
        deletedBy: null,
        createdOn: '',
        createdBy: '',
        modifiedOn: '',
        modifiedBy: '',
        version: '1',
        computeSize: '',
        dbSize: '',
      },
      deleted: false,
      deletedOn: null,
      deletedBy: null,
      createdOn: '',
      createdBy: '',
      modifiedOn: '',
      modifiedBy: '',
      description: '',
      size: '0',
      status: '1',
      version: '1',
      billingCycleId: '',
      currencyId: '',
      configureDeviceId: '',
      planSizeId: '',
    };
    renderWithTheme(<ChoosePlan {...defaultProps} setUserCount={setUserCount} overAllPlan={mockPlan} />);
    expect(screen.getByTestId('plan-summary')).toBeInTheDocument();
    expect(screen.getByText('Plan Summary')).toBeInTheDocument();
    expect(screen.getByText('Users: 2')).toBeInTheDocument();

    // Increase user count
    const incBtn = screen.getByTestId('increase-user-count');
    incBtn.click();
    expect(setUserCount).toHaveBeenCalledWith(3);

    // Decrease user count
    const decBtn = screen.getByTestId('decrease-user-count');
    decBtn.click();
    expect(setUserCount).toHaveBeenCalledWith(1);
  });

  it('renders error state when shouldShowError returns true', async () => {
    vi.resetModules();
    vi.doMock('./choosePlanHelpers', async () => {
      const actual = await vi.importActual<typeof import('./choosePlanHelpers')>('./choosePlanHelpers');
      return {
        ...actual,
        shouldShowError: () => true,
        shouldShowLoader: () => false,
      };
    });
    const {default: ChoosePlan} = await import('./ChoosePlan');
    render(<ChoosePlan {...defaultProps} />);
    await waitFor(() => {
      expect(screen.getByText(/Failed to fetch required data/i)).toBeInTheDocument();
    });
    // Reset helpers after error test
    vi.resetModules();
    vi.doMock('./choosePlanHelpers', helpersDefaultMock);
  });
});
