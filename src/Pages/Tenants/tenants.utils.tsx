import {CellContext} from '@tanstack/react-table';
import StatusChip from 'Components/StatusChip/StatusChip';
import {ActionButtons} from './TenantPage';

export const DEFAULT_LIMIT = 20;
export const DEFAULT_OFFSET = 0;

const whiteMain = 'white.main';

export enum TenantStatus {
  ACTIVE, // Tenant is active and fully functional
  PENDINGPROVISION, // Tenant is awaiting provisioning
  PROVISIONING, // Tenant is currently being provisioned
  PROVISIONFAILED, // Provisioning process failed
  INACTIVE, // Tenant is inactive
  PENDINGONBOARDING, // Tenant is active but not onboarded due to missing information
}

export const getStatusColor = (status: TenantStatus): string => {
  const statusColorMap: Record<number, string> = {
    [TenantStatus.ACTIVE]: `#E2FFF1`,
    [TenantStatus.PENDINGPROVISION]: `#FFF9E0`,
    [TenantStatus.INACTIVE]: `#FFE0E0`,
    [TenantStatus.PROVISIONFAILED]: `#FFE7DC`,
    [TenantStatus.PROVISIONING]: `#F3ECE4`,
    [TenantStatus.PENDINGONBOARDING]: `#F0F0FB`,
  };
  return statusColorMap[status] ?? whiteMain;
};

export const getFontColor = (status: TenantStatus): string => {
  const statusColorMap: Record<TenantStatus, string> = {
    [TenantStatus.ACTIVE]: `#0D653B`,
    [TenantStatus.PENDINGPROVISION]: `#736116`,
    [TenantStatus.INACTIVE]: `#A91417`,
    [TenantStatus.PROVISIONFAILED]: `#79310F`,
    [TenantStatus.PROVISIONING]: `#483014`,
    [TenantStatus.PENDINGONBOARDING]: `#17377F`,
  };
  return statusColorMap[status] || whiteMain;
};

export const getIndicatorColor = (status: TenantStatus): string => {
  const statusColorMap: Record<TenantStatus, string> = {
    [TenantStatus.ACTIVE]: `#1AC371`,
    [TenantStatus.PENDINGPROVISION]: `#736116`,
    [TenantStatus.INACTIVE]: `#FF1818`,
    [TenantStatus.PROVISIONFAILED]: `#FF7E42`,
    [TenantStatus.PROVISIONING]: `#9D815F`,
    [TenantStatus.PENDINGONBOARDING]: `#E7BF1B`,
  };
  return statusColorMap[status] || whiteMain;
};

export const getStatusLabel = (status: TenantStatus | number): string => {
  const statusLabelMap: Record<TenantStatus | number, string> = {
    [TenantStatus.ACTIVE]: 'Active',
    [TenantStatus.PENDINGPROVISION]: 'Pending Provision',
    [TenantStatus.INACTIVE]: 'Inactive',
    [TenantStatus.PROVISIONFAILED]: 'Failed Provision',
    [TenantStatus.PROVISIONING]: 'Provisioning',
    [TenantStatus.PENDINGONBOARDING]: 'Pending Onboarding',
  };
  return statusLabelMap[status] || '';
};

interface TenantTableRow {
  name: string;
  status: TenantStatus;
  createdOn: string;
  planName: string;
}

interface TenantTableColumn {
  header: string;
  accessorKey?: keyof TenantTableRow;
  id?: string;
  cell?: (context: CellContext<TenantTableRow, unknown>) => React.ReactNode;
}

const columnNameMap: Record<string, string> = {
  tenantName: 'name',
  status: 'status',
  createdDate: 'createdOn',
  planName: 'name',
};

export const getBackendColumnName = (columnName: string): string => columnNameMap[columnName] || columnName;

export const tenantTableColumns: TenantTableColumn[] = [
  {header: 'Tenant name', accessorKey: 'name', id: 'tenantName'},
  {
    header: 'Status',
    accessorKey: 'status',
    id: 'status',
    cell: (context: CellContext<TenantTableRow, unknown>) => {
      const status = context.getValue() as TenantStatus;
      const backgroundColor = getStatusColor(status);
      const color = getFontColor(status);
      const indicatorColor = getIndicatorColor(status);
      const label = getStatusLabel(status);
      return (
        <StatusChip label={label} backgroundColor={backgroundColor} indicatorColor={indicatorColor} color={color} />
      );
    },
  },
  {
    header: 'Created date',
    accessorKey: 'createdOn',
    id: 'createdDate',
    cell: ({row}: CellContext<TenantTableRow, unknown>) => {
      const date = new Date(row.original.createdOn);
      return date.toLocaleDateString('en-GB', {
        day: '2-digit',
        month: 'long',
        year: 'numeric',
      });
    },
  },
  {
    header: 'Plan name',
    accessorKey: 'planName',
    id: 'planName',
    cell: ({row}: CellContext<TenantTableRow, unknown>) => {
      const planName = row.original.planName;
      return planName ?? '-';
    },
  },
  {
    header: 'Actions',
    cell: (cellContext: CellContext<TenantTableRow, unknown>) => (
      <ActionButtons row={cellContext as CellContext<unknown, unknown>} />
    ),
  },
];
