import {fireEvent, screen} from '@testing-library/react';
import {apiSlice} from 'redux/apiSlice';
import {tenantApiSlice} from 'redux/app/tenantManagementApiSlice';
import {renderWithTheme} from 'TestHelper/TestHelper';
import {describe, expect, it, vi} from 'vitest';
import {DateRangeOptions} from '../../Components/DateRangeFilter/DateRangeFilter';
import TenantFilter, {ITenantFilter} from './TenantFilter';

const TEST_ID = {
  FILTER_TITLE: 'Filter',
  CLEAR_ALL: 'Clear all',
  CLOSE_BUTTON: 'Close',
  APPLY_BUTTON: 'Apply',
  DATE_SECTION_TITLE: 'Date',
} as const;

const STATUS_OPTIONS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
} as const;

const MOCK_ANCHOR_EL = document.createElement('div');

const defaultProps = {
  open: true,
  onClose: vi.fn(),
  anchorEl: MOCK_ANCHOR_EL,
} as const;

const mockFilterValue: ITenantFilter = {
  status: new Set([STATUS_OPTIONS.ACTIVE]),
  dateRange: {
    startDate: new Date('2024-01-01'),
    endDate: new Date('2024-12-31'),
    dateRangeOption: DateRangeOptions.CUSTOM,
  },
} as const;

describe('TenantFilter', () => {
  beforeEach(() => {
    // Reset RTK Query endpoints before each test
    apiSlice.util.resetApiState();

    // Mock the tenant statuses API response
    vi.spyOn(tenantApiSlice.endpoints.getAllTenantStatuses, 'useQuery').mockReturnValue({
      data: {
        statuses: {
          '0': 'Active',
          '4': 'Inactive',
        },
      },
      isLoading: false,
      isSuccess: true,
      isError: false,
      error: null,
      refetch: vi.fn(),
    } as any);
  });

  it('renders filter popover when open is true', () => {
    renderWithTheme(<TenantFilter {...defaultProps} />);

    expect(screen.getByText(TEST_ID.FILTER_TITLE)).toBeInTheDocument();
    expect(screen.getByText(TEST_ID.CLOSE_BUTTON)).toBeInTheDocument();
    expect(screen.getByText(TEST_ID.APPLY_BUTTON)).toBeInTheDocument();
    expect(screen.getByText(TEST_ID.DATE_SECTION_TITLE)).toBeInTheDocument();
  });

  it('does not render filter popover when open is false', () => {
    renderWithTheme(<TenantFilter {...defaultProps} open={false} />);

    expect(screen.queryByText(TEST_ID.FILTER_TITLE)).not.toBeInTheDocument();
  });

  it('calls onClose when close button is clicked', () => {
    renderWithTheme(<TenantFilter {...defaultProps} />);

    fireEvent.click(screen.getByText(TEST_ID.CLOSE_BUTTON));
    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  it('should apply filters and close when Apply button is clicked', () => {
    const onFilterChange = vi.fn();
    renderWithTheme(<TenantFilter {...defaultProps} value={mockFilterValue} onFilterChange={onFilterChange} />);

    fireEvent.click(screen.getByText(TEST_ID.APPLY_BUTTON));

    expect(onFilterChange).toHaveBeenCalledWith({
      status: mockFilterValue.status,
      dateRange: mockFilterValue.dateRange,
    });
    expect(defaultProps.onClose).toHaveBeenCalled();
  });

  describe('Date Range Filter', () => {
    it('selects predefined date range when clicked', () => {
      const onFilterChange = vi.fn();
      renderWithTheme(<TenantFilter {...defaultProps} onFilterChange={onFilterChange} />);

      fireEvent.click(screen.getByText(DateRangeOptions.LAST_MONTH));
      fireEvent.click(screen.getByText(TEST_ID.APPLY_BUTTON));

      expect(onFilterChange).toHaveBeenCalledWith(
        expect.objectContaining({
          dateRange: expect.objectContaining({
            dateRangeOption: DateRangeOptions.LAST_MONTH,
          }),
        }),
      );
    });
  });

  it('shows clear all button when filters are applied', () => {
    renderWithTheme(<TenantFilter {...defaultProps} value={mockFilterValue} />);

    expect(screen.getByText(TEST_ID.CLEAR_ALL)).toBeInTheDocument();
  });

  it('does not show clear all button when no filters are applied', () => {
    renderWithTheme(<TenantFilter {...defaultProps} />);

    expect(screen.queryByText(TEST_ID.CLEAR_ALL)).not.toBeInTheDocument();
  });

  it('clears all filters when clear all is clicked', () => {
    renderWithTheme(<TenantFilter {...defaultProps} value={mockFilterValue} />);

    fireEvent.click(screen.getByText(TEST_ID.CLEAR_ALL));

    // After clearing, the clear all button should not be visible
    expect(screen.queryByText(TEST_ID.CLEAR_ALL)).not.toBeInTheDocument();
  });
});
