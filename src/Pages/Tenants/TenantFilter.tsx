import {Box, Divider, IconButton, Typography} from '@mui/material';
import {PopoverWithArrow} from 'Components/AppBar/PopoverWithArrow';
import BlueButton from 'Components/BlueButton/BlueButton';
import BorderButton from 'Components/BorderButton/BorderButton';
import DefaultCloseIcon from 'Components/DefaultDialog/DefaultCloseIcon';
import {useCallback, useEffect, useRef, useState} from 'react';
import DateRangeFilter, {DateRangeFilterSelectedDate} from '../../Components/DateRangeFilter/DateRangeFilter';
import {IClearFilters} from '../../Components/FilterUtil/IClearFilters';
import FilterStatusChips, {FilterStatusChipsRef} from './StatusFilterChip/StatusFilterChip';

export interface ITenantFilter {
  status: Set<string>;
  dateRange: DateRangeFilterSelectedDate | undefined;
}

interface TenantFilterProps {
  open: boolean;
  onClose: () => void;
  value?: ITenantFilter;
  anchorEl?: HTMLElement | null;
  onFilterChange?: (filter: ITenantFilter) => void;
}

const paddingDefault = 2; // Default padding for the popover
const buttonHeight = '2.3125rem'; // Height for the buttons

/**
 * TenantFilter component that displays a filter popover for tenants.
 * @param props The props for the component.
 * @returns {JSX.Element} The rendered TenantFilter component.
 */
const TenantFilter: React.FC<TenantFilterProps> = props => {
  const {open, value: filter, anchorEl, onFilterChange, onClose} = props;
  const [selectedStatus, setSelectedStatus] = useState<Set<string>>(filter?.status ?? new Set());
  const [selectedRange, setSelectedRange] = useState<DateRangeFilterSelectedDate | undefined>(
    filter?.dateRange ?? undefined,
  );
  const filterStatusChipsRef = useRef<FilterStatusChipsRef>(null);
  const filterDateRangeRef = useRef<IClearFilters>(null);

  useEffect(() => {
    if (open) {
      // Reset selections when the filter is opened
      setSelectedStatus(filter?.status ?? new Set());
      setSelectedRange(filter?.dateRange);
    }
  }, [open]);

  const applyHandle = () => {
    onFilterChange?.({
      status: new Set(selectedStatus),
      dateRange: selectedRange,
    });
    onClose();
  };

  const handleClearSelection = useCallback(() => {
    filterStatusChipsRef.current?.clearSelection();
    filterDateRangeRef.current?.clearSelection();
  }, []);

  const buildButtonSection = () => {
    return (
      <Box sx={{display: 'flex', justifyContent: 'flex-end', p: paddingDefault, pt: 1, gap: 1}}>
        <BorderButton sx={{height: buttonHeight}} onClick={onClose}>
          Close
        </BorderButton>
        <BlueButton onClick={applyHandle} sx={{height: buttonHeight}}>
          Apply
        </BlueButton>
      </Box>
    );
  };
  const buildHeaderSection = () => {
    return (
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: paddingDefault,
          pb: 0,
        }}
      >
        <Typography>Filter</Typography>

        {showClearButton && (
          <Box
            onClick={handleClearSelection}
            sx={{
              ml: 'auto',
              p: 0,
              backgroundColor: 'transparent',
              color: 'secondary.main',
              fontWeight: 600,
              fontSize: '0.75rem',
              cursor: 'pointer',
              textDecoration: ' underline',
            }}
          >
            Clear all
          </Box>
        )}

        <IconButton onClick={onClose} size="small" sx={{padding: 0}}>
          <DefaultCloseIcon />
        </IconButton>
      </Box>
    );
  };

  const showClearButton = selectedStatus.size > 0 || selectedRange;

  return (
    <>
      {/* Popper sits above backdrop */}
      <PopoverWithArrow
        id="tenant-filter-popper"
        disablePortal={false}
        elevation={2}
        open={open}
        anchorEl={anchorEl}
        transformHOrigin={{
          horizontal: 'center',
        }}
        onClose={onClose}
      >
        <Box sx={{display: 'flex', flexDirection: 'column', gap: 1, minWidth: 220, maxWidth: 350}}>
          {buildHeaderSection()}
          <Divider />
          <Box sx={{padding: paddingDefault, pt: 0, display: 'flex', flexDirection: 'column', gap: 1}}>
            <FilterStatusChips ref={filterStatusChipsRef} onSelect={setSelectedStatus} value={selectedStatus} />
            <DateRangeFilter ref={filterDateRangeRef} onSelect={setSelectedRange} value={selectedRange} />
          </Box>
          <Divider />
          {buildButtonSection()}
        </Box>
      </PopoverWithArrow>
    </>
  );
};

export default TenantFilter;
