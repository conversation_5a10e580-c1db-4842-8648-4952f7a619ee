import {Typography} from '@mui/material';
import {ColumnDef} from '@tanstack/react-table';
import StatusChip from 'Components/StatusChip/StatusChip';
import {StatusChipState} from 'Components/StatusChip/statusChip.util';
import {DefaultToolTip} from 'Components/ToolTipTypography/ToolTipTypography';
import {defaultTableDateFormatting} from 'Constants/enums';
import {ITenantRoleByUser} from 'redux/auth/authApiSlice';
import {ActionButtons} from './ActionButtons';

/**
 * Enum for role status.
 */
export enum RoleStatus {
  ACTIVE = 0,
  INACTIVE = 1,
  UNKNOWN = -1,
}

/**
 * Maps role status to status chip state.
 * @param status - The role status to map.
 * @returns The corresponding status chip state.
 */
export const getChipStatus = (status: RoleStatus): StatusChipState => {
  const chipStatusMap: Record<RoleStatus, StatusChipState> = {
    [RoleStatus.ACTIVE]: StatusChipState.ACTIVE,
    [RoleStatus.INACTIVE]: StatusChipState.INACTIVE,
    [RoleStatus.UNKNOWN]: StatusChipState.INACTIVE,
  };
  return chipStatusMap[status] ?? StatusChipState.INACTIVE;
};

/**
 * Defines the columns for the roles table.
 */
export const columns: ColumnDef<ITenantRoleByUser>[] = [
  {
    header: 'Role Name',
    accessorKey: 'roleName',
    cell: ({row}) => (
      <DefaultToolTip title={row.original.roleName} placement="left" arrow>
        <Typography>{row.original.roleName}</Typography>
      </DefaultToolTip>
    ),
  },
  {
    header: 'No. of users',
    accessorKey: 'userCount',
    cell: ({row}) => (
      <DefaultToolTip title={row.original.userCount} placement="left" arrow>
        <Typography>{row.original.userCount}</Typography>
      </DefaultToolTip>
    ),
  },
  {
    header: 'Status',
    accessorKey: 'status',
    cell: ({row}) => <StatusChip status={getChipStatus(row.original.status)} />,
  },
  {
    header: 'Created On',
    accessorKey: 'createdOn',
    cell: ({row}) => {
      const date = defaultTableDateFormatting(row.original.createdOn);
      return (
        <DefaultToolTip title={date} placement="left" arrow>
          <Typography>{date}</Typography>
        </DefaultToolTip>
      );
    },
  },

  {
    header: 'Modified On',
    accessorKey: 'modifiedOn',
    cell: ({row}) => {
      const date = defaultTableDateFormatting(row.original.modifiedOn);
      return (
        <DefaultToolTip title={date} placement="left" arrow>
          <Typography>{date}</Typography>
        </DefaultToolTip>
      );
    },
  },
  {
    header: 'Actions',
    accessorKey: 'actions',
    cell: ({row}) => <ActionButtons role={row.original} />,
  },
];
