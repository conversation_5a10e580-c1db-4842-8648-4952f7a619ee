import {Box, Typography} from '@mui/material';
import BackdropLoader from 'Components/BackdropLoader';
import {useTableState} from 'Components/Table/hook/TableStateHook';
import {defaultTableRowsPerPageArr} from 'Constants/enums';
import {useMemo, useState} from 'react';
import {IApiFilter} from 'redux/app/types';
import {useGetCountOfRolesByGroupQuery, useGetRolesByGroupQuery} from 'redux/auth/authApiSlice';
import {commonHeaderBoxStyle} from 'styles/pages/Common.styles';
import {
  bodyCellProps,
  coloumnCellProps,
  leftHeaderStyle,
  tableContainerProps,
  tableHeadProps,
} from 'styles/pages/TenantPage.styles';
import {Table} from '../../Components/Table/Table';
import NoRoleView from './NoRoleView';
import {columns} from './roles.util';

/**
 * Roles & Responsibilities Page
 * This page displays roles and responsibilities for the user.
 * May have features like add search and sorting of some columns
 */
const RolesResponsibilitiesPage = () => {
  const {limit, setLimit, offset, setOffset, handlePageChange, handleRowsPerPageChange} = useTableState();
  const [sortBy, setSortBy] = useState<string>('createdOn DESC');
  const initialSortState = [{id: 'createdOn', desc: true}];

  // Build the filter object for the API call
  const filterParams: IApiFilter = useMemo(() => {
    return {
      limit,
      offset,
      order: sortBy,
    };
  }, [limit, offset, sortBy]);

  // Build count filter (without limit/offset)
  const countFilterParams: IApiFilter = useMemo(() => {
    return {
      order: sortBy,
    };
  }, [sortBy]);

  const {
    data: roles = [],
    error,
    isLoading,
  } = useGetRolesByGroupQuery(filterParams, {
    refetchOnMountOrArgChange: true,
  });

  const {
    data: roleCount,
    error: roleCountError,
    isLoading: isRoleCountLoading,
  } = useGetCountOfRolesByGroupQuery(countFilterParams, {
    refetchOnMountOrArgChange: true,
  });

  const handleSortChange = (columnId: string, sort: boolean) => {
    const sortParam = `${columnId} ${sort ? 'DESC' : 'ASC'}`;
    setSortBy(sortParam);
  };

  if (isLoading || isRoleCountLoading) {
    return <BackdropLoader />;
  }

  if (error || roleCountError) {
    return (
      <Box component={'h2'} sx={{width: '100%', textAlign: 'center'}}>
        Something went wrong. Please try again later.
      </Box>
    );
  }

  const drawTable = () => {
    return (
      <Table
        data={roles ?? []}
        columns={columns}
        enableSorting
        initialSortingState={initialSortState}
        tablePropsObject={{
          tableHeadProps: {sx: tableHeadProps},
          columnCellProps: {sx: coloumnCellProps},
          tableContainerProps: {sx: tableContainerProps},
          bodyCellProps: {sx: bodyCellProps},
        }}
        limit={limit}
        setLimit={setLimit}
        offset={offset}
        setOffset={setOffset}
        count={roleCount?.count ?? 0}
        manualPagination={true}
        onSortChange={handleSortChange}
        onPageChange={handlePageChange}
        onRowsPerPageChange={handleRowsPerPageChange}
        data-testid="role-table"
        rowsPerPageOptions={defaultTableRowsPerPageArr}
        excludeSortColumns={['status', 'actions']}
      />
    );
  };

  return (
    <Box sx={{padding: '24px'}}>
      {/* Header */}
      <Box sx={commonHeaderBoxStyle}>
        <Typography variant="h6" sx={leftHeaderStyle}>
          Roles & Responsibilities
        </Typography>
      </Box>
      {roles.length === 0 ? (
        <Box sx={{display: 'flex', justifyContent: 'center', alignItems: 'center', height: '70vh'}}>
          <NoRoleView />
        </Box>
      ) : (
        drawTable()
      )}
    </Box>
  );
};

export default RolesResponsibilitiesPage;
