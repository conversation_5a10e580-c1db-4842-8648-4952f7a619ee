import {Stack} from '@mui/material';
import EditIcon from 'Assets/EditIcon';
import EyeIcon from 'Assets/EyeIcon';
import RoleMarkActiveIcon from 'Assets/RoleMarkActiveIcon';
import RoleMarkDeactivateIcon from 'Assets/RoleMarkDeactivateIcon';
import {DefaultToolTip} from 'Components/ToolTipTypography/ToolTipTypography';
import React from 'react';
import {ITenantRoleByUser} from 'redux/auth/authApiSlice';
import {actionStyles} from 'styles/pages/Common.styles';
import {toolTipStyles} from 'styles/pages/TenantPage.styles';
import {RoleStatus} from './roles.util';

/**
 * ActionButtons component for Role and Permissions
 * @param props - Component props
 * @returns JSX.Element
 */
export const ActionButtons: React.FC<{role: ITenantRoleByUser}> = props => {
  const buildStatusAction = () => {
    if (props.role.status === RoleStatus.ACTIVE) {
      return (
        <DefaultToolTip title="Deactivate" placement="top" arrow data-testid="deactivate-button">
          <RoleMarkDeactivateIcon sx={actionStyles} />
        </DefaultToolTip>
      );
    } else if (props.role.status === RoleStatus.INACTIVE) {
      return (
        <DefaultToolTip title="Activate" placement="top" arrow slotProps={toolTipStyles} data-testid="activate-button">
          <RoleMarkActiveIcon sx={actionStyles} />
        </DefaultToolTip>
      );
    } else {
      return null;
    }
  };

  return (
    <Stack display="flex" flexDirection={'row'} gap={1.4}>
      <DefaultToolTip title="View details" placement="top" arrow slotProps={toolTipStyles} data-testid="view-button">
        <EyeIcon sx={actionStyles} />
      </DefaultToolTip>
      <DefaultToolTip title="Edit" placement="top" arrow slotProps={toolTipStyles} data-testid="edit-button">
        <EditIcon sx={actionStyles} />
      </DefaultToolTip>
      {buildStatusAction()}
    </Stack>
  );
};
