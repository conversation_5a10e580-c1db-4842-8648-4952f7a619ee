import {CellContext} from '@tanstack/react-table';
import {cleanup, render, screen} from '@testing-library/react';
import {StatusChipState} from 'Components/StatusChip/statusChip.util';
import {defaultTableDateFormatting} from 'Constants/enums';
import type {ITenantRoleByUser} from 'redux/auth/authApiSlice';
import {describe, expect, it, vi} from 'vitest';
import {columns, getChipStatus, RoleStatus} from './roles.util';

vi.mock('Components/StatusChip/StatusChip', () => ({
  default: ({status}: {status: StatusChipState}) => <div data-testid="status-chip" data-status={status} />,
}));

vi.mock('Components/ToolTipTypography/ToolTipTypography', () => ({
  DefaultToolTip: ({children}: {children: React.ReactNode}) => <div data-testid="tooltip">{children}</div>,
}));

vi.mock('./ActionButtons', () => ({
  ActionButtons: ({role}: {role: ITenantRoleByUser}) => (
    <div data-testid="action-buttons" data-role={JSON.stringify(role)} />
  ),
}));

describe('roles.util', () => {
  afterEach(cleanup);

  describe('getChipStatus', () => {
    it('maps role status to correct chip state', () => {
      expect(getChipStatus(RoleStatus.ACTIVE)).toBe(StatusChipState.ACTIVE);
      expect(getChipStatus(RoleStatus.INACTIVE)).toBe(StatusChipState.INACTIVE);

      const invalidStatus = 999 as RoleStatus;
      expect(getChipStatus(invalidStatus)).toBe(StatusChipState.INACTIVE);
    });
  });

  describe('RoleStatus enum', () => {
    it('has correct values', () => {
      expect(RoleStatus.ACTIVE).toBe(0);
      expect(RoleStatus.INACTIVE).toBe(1);
    });
  });

  describe('columns', () => {
    const mockData: ITenantRoleByUser = {
      roleName: 'Admin',
      userCount: 5,
      status: RoleStatus.ACTIVE,
      createdOn: '2023-08-12T10:00:00Z',
      modifiedOn: '2023-08-12T11:00:00Z',
      roleId: '123',
      tenantId: '456',
    };

    const createTestContext = (data: ITenantRoleByUser): CellContext<ITenantRoleByUser, unknown> =>
      ({
        row: {
          original: data,
        },
      }) as CellContext<ITenantRoleByUser, unknown>;

    it('has correct header values', () => {
      const expectedHeaders = ['Role Name', 'No. of users', 'Status', 'Created On', 'Modified On', 'Actions'];
      columns.forEach((column, index) => {
        expect(column.header).toBe(expectedHeaders[index]);
      });
    });

    it('renders role name correctly', () => {
      const columnDef = columns[0];
      if (typeof columnDef.cell === 'function') {
        const {container} = render(<>{columnDef.cell(createTestContext(mockData))}</>);
        expect(container.textContent).toBe('Admin');
      }
    });

    it('renders user count correctly', () => {
      const columnDef = columns[1];
      if (typeof columnDef.cell === 'function') {
        const {container} = render(<>{columnDef.cell(createTestContext(mockData))}</>);
        expect(container.textContent).toBe('5');
      }
    });

    it('renders status with correct chip state', () => {
      const columnDef = columns[2];
      if (typeof columnDef.cell === 'function') {
        render(<>{columnDef.cell(createTestContext(mockData))}</>);
        expect(screen.getByTestId('status-chip').getAttribute('data-status')).toBe(String(StatusChipState.ACTIVE));
      }
    });

    it('renders status for inactive state', () => {
      const inactiveData = {...mockData, status: RoleStatus.INACTIVE};
      const columnDef = columns[2];
      if (typeof columnDef.cell === 'function') {
        render(<>{columnDef.cell(createTestContext(inactiveData))}</>);
        expect(screen.getByTestId('status-chip').getAttribute('data-status')).toBe(String(StatusChipState.INACTIVE));
      }
    });

    it('renders dates correctly', () => {
      // Created On
      const createdColumnDef = columns[3];
      if (typeof createdColumnDef.cell === 'function') {
        const {container} = render(<>{createdColumnDef.cell(createTestContext(mockData))}</>);
        expect(container.textContent).toBe(defaultTableDateFormatting(mockData.createdOn));
      }

      // Modified On
      const modifiedColumnDef = columns[4];
      if (typeof modifiedColumnDef.cell === 'function') {
        const {container} = render(<>{modifiedColumnDef.cell(createTestContext(mockData))}</>);
        expect(container.textContent).toBe(defaultTableDateFormatting(mockData.modifiedOn));
      }
    });

    it('renders actions with correct role data', () => {
      const columnDef = columns[5];
      if (typeof columnDef.cell === 'function') {
        render(<>{columnDef.cell(createTestContext(mockData))}</>);
        const actionButtons = screen.getByTestId('action-buttons');
        expect(JSON.parse(actionButtons.getAttribute('data-role') || '')).toEqual(mockData);
      }
    });
  });
});
