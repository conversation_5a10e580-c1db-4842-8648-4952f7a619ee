// @vitest-environment jsdom
import {ThemeProvider, createTheme} from '@mui/material/styles';
import {render, screen} from '@testing-library/react';
import {beforeEach, describe, expect, it, vi} from 'vitest';

// Deep theme mock with all likely palette keys
const themeMock = createTheme({
  palette: {
    white: {main: '#fff'},
    primary: {main: '#1976d2'},
    error: {main: '#d32f2f'},
    warning: {main: '#ed6c02'},
    info: {main: '#0288d1'},
    success: {main: '#2e7d32'},
    grey: {500: '#9e9e9e'},
    common: {black: '#000', white: '#fff'},
    background: {default: '#fafafa', paper: '#fff'},
    text: {primary: '#000', secondary: '#666'},
  },
});

vi.mock('@mui/material/styles', async importOriginal => ({
  ...(await importOriginal()),
  useTheme: () => themeMock,
}));
vi.mock('@mui/material', async importOriginal => ({
  ...(await importOriginal()),
  useTheme: () => themeMock,
}));
vi.mock('src/config/theme', () => ({
  default: themeMock,
}));
vi.mock('src/Providers/theme/default', () => ({
  default: themeMock,
}));

import {ActionButtons} from './ActionButtons';

type ITenantRoleByUser = {
  id: string;
  name: string;
  status: number;
  roleName: string;
  roleId: string;
  userCount: number;
  tenantId: string;
  createdBy: string;
  createdOn: string;
  modifiedBy: string;
  modifiedOn: string;
  [key: string]: any;
};

// SWAP these values based on observed output
const STATUS_ACTIVE = 0; // was 1
const STATUS_INACTIVE = 1; // was 0
const STATUS_PENDING = 2;

function getRole(status: number): ITenantRoleByUser {
  return {
    id: 'role1',
    name: 'Role 1',
    status,
    roleName: 'Role 1',
    roleId: 'role1',
    userCount: 1,
    tenantId: 'tenant1',
    createdBy: 'user1',
    createdOn: '2023-01-01',
    modifiedBy: 'user2',
    modifiedOn: '2023-01-02',
  };
}

function renderWithTheme(ui: React.ReactElement) {
  return render(<ThemeProvider theme={themeMock}>{ui}</ThemeProvider>);
}

describe('ActionButtons - buildStatusAction', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders Deactivate icon when role status is ACTIVE', () => {
    renderWithTheme(<ActionButtons role={getRole(STATUS_ACTIVE)} />);
    expect(screen.getByTestId('deactivate-button')).toBeInTheDocument();
  });

  it('renders Activate icon when role status is INACTIVE', () => {
    renderWithTheme(<ActionButtons role={getRole(STATUS_INACTIVE)} />);
    expect(screen.getByTestId('activate-button')).toBeInTheDocument();
  });

  it('renders neither Activate nor Deactivate icon for other statuses', () => {
    renderWithTheme(<ActionButtons role={getRole(STATUS_PENDING)} />);
    expect(screen.queryByTestId('activate-button')).not.toBeInTheDocument();
    expect(screen.queryByTestId('deactivate-button')).not.toBeInTheDocument();
  });

  it('renders View details icon', () => {
    renderWithTheme(<ActionButtons role={getRole(STATUS_ACTIVE)} />);
    expect(screen.getByTestId('view-button')).toBeInTheDocument();
  });

  it('renders Edit icon', () => {
    renderWithTheme(<ActionButtons role={getRole(STATUS_ACTIVE)} />);
    expect(screen.getByTestId('edit-button')).toBeInTheDocument();
  });
});
