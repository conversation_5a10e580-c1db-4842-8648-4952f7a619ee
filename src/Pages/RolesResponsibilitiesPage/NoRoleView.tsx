import {Box, Stack, Typography} from '@mui/material';
import NoRoleIcon from 'Assets/NoRoleIcon';
/**
 * NoRoleView component when no role is available to render
 * @returns {JSX.Element}
 */
const NoRoleView = () => {
  return (
    <Stack spacing={0.5} alignItems="center" justifyContent="center" flexDirection={'column'}>
      <Box sx={{width: '10.6875rem', aspectRatio: 1, backgroundColor: 'primary.50', borderRadius: '50%'}}>
        <NoRoleIcon sx={{width: '100%', height: '100%', p: 2.75}} />
      </Box>
      <Typography sx={{fontWeight: 700, fontSize: '1.375rem', color: 'body.dark'}}>
        You haven’t created any roles yet
      </Typography>
      <Typography textAlign={'center'} sx={{color: 'body.500', fontSize: '1rem', fontWeight: 400}}>
        Create a role to define permissions and manage <br /> user access effectively.
      </Typography>
    </Stack>
  );
};

export default NoRoleView;
