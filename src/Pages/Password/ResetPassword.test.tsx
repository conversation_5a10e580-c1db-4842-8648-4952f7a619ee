// Copyright (c) Repfabric. All rights reserved.

import {screen, waitFor} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import {MemoryRouter, Route, Routes} from 'react-router-dom';
import {renderWithThemedStore} from 'Tests/utils/renderWithStore';
import {beforeEach, describe, expect, it, vi} from 'vitest';
import ResetPassword from './ResetPassword';

// Mock RTK Query mutation
const mockResetPassword = vi.fn();
vi.mock('redux/auth/authApiSlice', () => ({
  useResetPasswordMutation: () => [mockResetPassword, {isLoading: false}],
}));

describe('ResetPassword Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockResetPassword.mockReset();
    mockResetPassword.mockReturnValue({
      unwrap: vi.fn().mockResolvedValue({}),
    });
  });

  const renderResetPassword = (url = '/reset-password?code=test-token') =>
    renderWithThemedStore(
      <MemoryRouter initialEntries={[url]}>
        <Routes>
          <Route path="/reset-password" element={<ResetPassword />} />
        </Routes>
      </MemoryRouter>,
    );

  it('renders the reset password form', () => {
    renderResetPassword();
    expect(screen.getByTestId('ResetPasswordPage')).toBeInTheDocument();
    // Check at least one heading with the main title exists
    const headings = screen.getAllByRole('heading', {name: /Reset your password/i});
    expect(headings.length).toBeGreaterThan(0);
    expect(screen.getByText('New Password')).toBeInTheDocument();
    expect(screen.getByText('Confirm New Password')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Set your new password')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Confirm your new password')).toBeInTheDocument();
    expect(screen.getByRole('button', {name: /Change Password/i})).toBeInTheDocument();
  });

  it('shows validation error when fields are empty', async () => {
    renderResetPassword();
    const newPasswordInput = screen.getByPlaceholderText('Set your new password');
    const confirmPasswordInput = screen.getByPlaceholderText('Confirm your new password');
    await userEvent.click(newPasswordInput);
    await userEvent.tab();
    await userEvent.click(confirmPasswordInput);
    await userEvent.tab();
    await waitFor(() => {
      expect(screen.getByText('Confirm password is required')).toBeInTheDocument();
    });
  });

  it('shows validation error when passwords do not match', async () => {
    renderResetPassword();
    const newPasswordInput = screen.getByPlaceholderText('Set your new password');
    const confirmPasswordInput = screen.getByPlaceholderText('Confirm your new password');
    await userEvent.type(newPasswordInput, 'Password@123');
    await userEvent.type(confirmPasswordInput, 'Password@124');
    await userEvent.tab();
    await waitFor(() => {
      expect(screen.getByText('Passwords must match')).toBeInTheDocument();
    });
  });

  it('shows password requirements and updates as user types', async () => {
    renderResetPassword();
    const newPasswordInput = screen.getByPlaceholderText('Set your new password');
    await userEvent.type(newPasswordInput, 'Pass');
    expect(screen.getByText('Minimum 8 characters')).toBeInTheDocument();
    expect(screen.getByText('At least one uppercase letter')).toBeInTheDocument();
    expect(screen.getByText('At least one lowercase letter')).toBeInTheDocument();
    expect(screen.getByText('At least one special character')).toBeInTheDocument();
    expect(screen.getByText('At least one number')).toBeInTheDocument();
  });

  it('shows error if not all password requirements are met', async () => {
    renderResetPassword();
    const newPasswordInput = screen.getByPlaceholderText('Set your new password');
    const confirmPasswordInput = screen.getByPlaceholderText('Confirm your new password');
    await userEvent.type(newPasswordInput, 'short');
    await userEvent.type(confirmPasswordInput, 'short');
    const submitButton = screen.getByRole('button', {name: /Change Password/i});
    await userEvent.click(submitButton);
    await waitFor(() => {
      expect(screen.getByText(/Please ensure all password requirements are met/i)).toBeInTheDocument();
    });
  });

  it('calls resetPassword mutation on valid submit', async () => {
    renderResetPassword();
    const newPasswordInput = screen.getByPlaceholderText('Set your new password');
    const confirmPasswordInput = screen.getByPlaceholderText('Confirm your new password');
    await userEvent.type(newPasswordInput, 'Password@123');
    await userEvent.type(confirmPasswordInput, 'Password@123');
    const submitButton = screen.getByRole('button', {name: /Change Password/i});
    await userEvent.click(submitButton);
    await waitFor(() => {
      expect(mockResetPassword).toHaveBeenCalledWith({newPassword: 'Password@123', token: 'test-token'});
    });
  });

  it('shows error if no reset token is present', async () => {
    renderResetPassword('/reset-password');
    const newPasswordInput = screen.getByPlaceholderText('Set your new password');
    const confirmPasswordInput = screen.getByPlaceholderText('Confirm your new password');
    await userEvent.type(newPasswordInput, 'Password@123');
    await userEvent.type(confirmPasswordInput, 'Password@123');
    const submitButton = screen.getByRole('button', {name: /Change Password/i});
    await userEvent.click(submitButton);
    await waitFor(() => {
      expect(screen.getByText(/No token found/i)).toBeInTheDocument();
    });
  });

  it('shows error message on mutation error', async () => {
    mockResetPassword.mockReturnValueOnce({
      unwrap: vi.fn().mockRejectedValue(new Error('Failed to set new password. Please try again.')),
    });
    renderResetPassword();
    const newPasswordInput = screen.getByPlaceholderText('Set your new password');
    const confirmPasswordInput = screen.getByPlaceholderText('Confirm your new password');
    await userEvent.type(newPasswordInput, 'Password@123');
    await userEvent.type(confirmPasswordInput, 'Password@123');
    const submitButton = screen.getByRole('button', {name: /Change Password/i});
    await userEvent.click(submitButton);
    await waitFor(() => {
      expect(screen.getByText(/Failed to set new password/i)).toBeInTheDocument();
    });
  });

  it('shows success state and "Go To Sign in" button after password change', async () => {
    renderResetPassword();
    const newPasswordInput = screen.getByPlaceholderText('Set your new password');
    const confirmPasswordInput = screen.getByPlaceholderText('Confirm your new password');
    await userEvent.type(newPasswordInput, 'Password@123');
    await userEvent.type(confirmPasswordInput, 'Password@123');
    const submitButton = screen.getByRole('button', {name: /Change Password/i});
    await userEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/Password updated successfully/i)).toBeInTheDocument();
      expect(screen.getByRole('button', {name: /Go To Sign in/i})).toBeInTheDocument();
    });
  });

  it('renders "Sign in" link with correct href', () => {
    renderResetPassword();
    const signInLink = screen.getByText(/Sign in/i);
    expect(signInLink).toBeInTheDocument();
    expect(signInLink.closest('a')).toHaveAttribute('href', '/login');
  });
});
