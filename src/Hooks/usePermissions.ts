import {useEffect, useState} from 'react';
import {selectCurrentAuthState} from 'redux/auth/authSlice';
import {useAppSelector} from 'redux/hooks';

/**
 * Custom hook to manage user permissions. It provides a function to check if a user has a specific permission.
 * We can use this hook in any component to easily check user permissions.
 * Can conditionally render components based on user permissions.
 * @returns An object containing a function to check permissions.
 */
export const usePermissions = () => {
  const authState = useAppSelector(selectCurrentAuthState);

  const [permissions, setPermissions] = useState<Set<string>>(new Set());

  useEffect(() => {
    const components = (authState.accessToken ?? '').split('.');
    const ExpectedComponentLength = 3;
    if (components.length === ExpectedComponentLength) {
      const payload = JSON.parse(atob(components[1]));
      setPermissions(new Set(payload.permissions ?? []));
    }
  }, [authState]);

  const hasPermission = (permission: string): boolean => permissions.has(permission);

  return {
    hasPermission,
  };
};
