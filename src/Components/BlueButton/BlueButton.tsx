import {ButtonProps, SxProps} from '@mui/material';
import Button from 'Components/Button';
import {BtnProps} from 'Components/Button/Button';
import {forwardRef} from 'react';
export interface BlueButtonProps extends ButtonProps {
  onClick?: () => void;
  children: React.ReactNode;
  sx?: SxProps;
}
/**
 * BlueButton component that renders a button with a blue back style.
 * It accepts onClick handler, children, and additional styles via sx prop.
 */

const whiteMain = 'white.main';

const BlueButton = forwardRef<HTMLButtonElement, BtnProps>(({onClick, children, sx, ...props}, ref) => {
  return (
    <Button
      ref={ref}
      variant="outlined"
      sx={{
        borderColor: 'body.100',
        borderRadius: '0.375rem',
        color: whiteMain,
        fontWeight: 'inherit',
        fontSize: 'inherit',
        backgroundColor: 'secondary.main',
        '&:hover': {
          backgroundColor: 'secondary.650',
        },
        '&.selected': {
          backgroundColor: 'secondary.700',
        },
        '& .MuiTouchRipple-root .MuiTouchRipple-rippleVisible': {
          color: 'secondary.700',
        },
        '&.Mui-disabled': {
          backgroundColor: 'secondary.100',
          color: whiteMain, // optional: set disabled text color
        },
        ...sx,
      }}
      onClick={onClick}
      {...props}
    >
      {children}
    </Button>
  );
});
BlueButton.displayName = 'BlueButton';

export default BlueButton;
