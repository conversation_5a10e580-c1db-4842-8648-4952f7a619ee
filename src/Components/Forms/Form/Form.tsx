import {Formik, Form as FormikForm, FormikValues} from 'formik';
import {ForwardedRef, forwardRef, JSX, ReactNode} from 'react';
import * as yup from 'yup';
import {FormActions} from './FormUtils';

/**
 * Props for the reusable Form component.
 *
 * @template T - The shape of the form values.
 */
export interface Props<T extends FormikValues> {
  /** Initial values for the form fields */
  initialValues: T;

  /**
   * Function to handle form submission
   * @param values - Current form values
   * @param actions - Formik actions (reset, setSubmitting, etc.)
   */
  onSubmit: (values: T, actions: FormActions<T>) => Promise<void>;

  /** Optional Yup validation schema */
  validationSchema?: yup.ObjectSchema<yup.AnyObject>;

  /** Optional form ID */
  id?: string;

  /** Whether to reinitialize the form if initialValues change */
  enableReinitialize?: boolean;

  /** Child components (form fields, etc.) */
  children?: ReactNode;

  /** Whether to validate on blur */
  validateOnBlur?: boolean;

  /** Whether to validate on change */
  validateOnChange?: boolean;
}

/**
 * Inner functional component that wraps Formik and provides form rendering logic.
 *
 * @template T - The shape of the form values.
 * @param props - The form props.
 * @param ref - Ref to the HTMLFormElement.
 * @returns Formik wrapped form JSX.
 */
function InnerForm<T extends FormikValues>(
  {
    initialValues,
    onSubmit,
    validationSchema,
    children,
    id,
    enableReinitialize = false,
    validateOnBlur = true,
    validateOnChange = false,
  }: Props<T>,
  ref: ForwardedRef<HTMLFormElement>,
) {
  return (
    <Formik<T>
      enableReinitialize={enableReinitialize}
      initialValues={initialValues}
      onSubmit={onSubmit}
      validationSchema={validationSchema}
      validateOnBlur={validateOnBlur}
      validateOnChange={validateOnChange}
    >
      {({handleSubmit}) => (
        <FormikForm
          id={id}
          role="form"
          onSubmit={event => {
            event.preventDefault();
            handleSubmit(event);
          }}
          ref={ref}
        >
          {children}
        </FormikForm>
      )}
    </Formik>
  );
}

// Generic-aware forwardRef wrapper
const Forwarded = forwardRef(InnerForm);
Forwarded.displayName = 'Form';

/**
 * Reusable, generic-aware Form component using Formik and forwardRef.
 *
 * @template T - The shape of the form values.
 */
const _Form = Forwarded as <T extends FormikValues>(
  props: Props<T> & {ref?: ForwardedRef<HTMLFormElement>},
) => JSX.Element;

export default _Form;
