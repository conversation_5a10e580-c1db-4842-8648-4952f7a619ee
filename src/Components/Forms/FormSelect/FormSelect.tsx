'use client';

import type {InputProps} from 'Components/Input/Input';
import SelectInput from 'Components/Input/select-input';
import {type FormikValues, useFormikContext} from 'formik';
import {getValue} from 'Helpers/utils';
import type React from 'react';
import {useCallback} from 'react';

/**
 * Represents an option in the select dropdown.
 */
interface SelectOption {
  value: string;
  label: string;
}

/**
 * Props for the FormSelect component.
 */
interface FormSelectProps extends Omit<InputProps, 'onChange'> {
  /**
   * Field ID used to identify the form field in Formik.
   */
  id: string;

  /**
   * List of options to show in the dropdown.
   */
  options: SelectOption[];

  /**
   * Placeholder text for the dropdown.
   */
  placeholder?: string;

  /**
   * Flag to disable the dropdown.
   */
  disabled?: boolean;

  /**
   * Optional custom change handler.
   */
  onChange?: (val: string) => void;

  /**
   * Optional custom renderer for each option.
   */
  renderOption?: (option: SelectOption) => React.ReactNode;

  /**
   * Optional custom renderer for the selected value.
   */
  renderValue?: (selected: string, options: SelectOption[]) => React.ReactNode;

  /**
   * Placement of the dropdown menu: 'bottom' (default) or 'top'
   */
  menuPlacement?: 'bottom' | 'top';
}

/**
 * A reusable FormSelect component that integrates with Formik form context.
 *
 * Automatically handles value binding, error state, and updates Formik state on change.
 *
 * @component
 * @param {FormSelectProps} props - Props for the component.
 * @returns {JSX.Element} The rendered select input field.
 */
const FormSelect: React.FC<FormSelectProps> = ({
  id,
  disabled,
  options,
  placeholder,
  renderOption,
  renderValue,
  menuPlacement,
  ...rest
}) => {
  const {setFieldValue, errors, touched, values, setFieldTouched} = useFormikContext<FormikValues>();
  const isError = !!getValue(errors, id) && getValue(touched, id) && !disabled;

  const handleChangeEvent = useCallback((val: string) => setFieldValue(id, val), [id, setFieldValue]);
  const handleBlurEvent = useCallback(() => setFieldTouched(id, true), [id, setFieldTouched]);

  return (
    <SelectInput
      id={id}
      value={getValue(values, id)}
      errorMessage={isError ? getValue(errors, id) : ''}
      disabled={disabled}
      onChange={handleChangeEvent}
      onBlur={handleBlurEvent}
      options={options}
      placeholder={placeholder}
      renderOption={renderOption}
      renderValue={renderValue ? selected => renderValue(String(selected), options) : undefined}
      menuPlacement={menuPlacement}
      {...rest}
    />
  );
};

export default FormSelect;
