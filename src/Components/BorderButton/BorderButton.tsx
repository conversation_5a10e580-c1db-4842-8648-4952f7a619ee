import {SxProps} from '@mui/material';
import Button from 'Components/Button';
import {forwardRef} from 'react';

export interface BorderButtonProps {
  onClick?: () => void;
  children?: React.ReactNode;
  sx?: SxProps;
}

/**
 * BorderButton component that renders a button with a border style.
 * It accepts onClick handler, children, and additional styles via sx prop.
 */

const BorderButton = forwardRef<HTMLButtonElement, BorderButtonProps>(({onClick, children, sx}, ref) => {
  return (
    <Button
      ref={ref}
      variant="outlined"
      sx={{
        borderColor: 'body.100',
        borderWidth: 1,
        borderRadius: '0.375rem',
        backgroundColor: 'white.main',
        fontSize: 'inherit',
        fontWeight: 'inherit',
        color: 'body.800',
        ...sx,
      }}
      onClick={onClick}
    >
      {children}
    </Button>
  );
});

export default BorderButton;
