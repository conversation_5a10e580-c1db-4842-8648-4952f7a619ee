import {Box} from '@mui/material';
import LogoutIcon from 'Assets/logout-dialog-ic.svg';
import BlueButton from 'Components/BlueButton/BlueButton';
import BorderButton from 'Components/BorderButton/BorderButton';
import {DefaultDialog} from 'Components/DefaultDialog/DefaultDialog';
import ToolTipTypography from 'Components/ToolTipTypography/ToolTipTypography';

export const LogoutDialogIcon = () => {
  return (
    <Box
      sx={{
        width: 80,
        height: 80,
        backgroundColor: 'secondary.50',
        alignItems: 'center',
        display: 'flex',
        justifyContent: 'center',
        borderRadius: '50%',
      }}
    >
      <Box component={'img'} src={LogoutIcon} alt="logout-icon" />
    </Box>
  );
};

interface LogoutDialogProps {
  open: boolean;
  onClose: () => void;
  onLogout: () => void;
}

export const LogoutDialog = ({open, onClose, onLogout}: LogoutDialogProps) => {
  return (
    <DefaultDialog title={'Logout'} maxWidth={400} open={open} onClose={onClose}>
      <Box sx={{display: 'flex', flexDirection: 'column', alignItems: 'center', mt: 4}}>
        <LogoutDialogIcon />
        <ToolTipTypography
          message="Are you sure you want to log out of Repfabric?"
          sx={{mt: 2, fontSize: '1.125rem', fontWeight: 700, color: 'body.dark', textAlign: 'center'}}
        />

        <ToolTipTypography
          message="You'll need to sign in again to access your data."
          sx={{mt: 2, fontSize: '0.815rem', fontWeight: 600, color: 'body.500'}}
        />
        <Box
          sx={{mt: 4, display: 'flex', gap: 1, flexDirection: 'row', width: '100%', fontSize: '1rem', fontWeight: 600}}
        >
          <BorderButton sx={{flex: 1}} onClick={onClose}>
            Stay Logged In
          </BorderButton>
          <BlueButton
            data-testid="logout-button"
            sx={{
              flex: 1,
            }}
            onClick={onLogout}
          >
            Logout
          </BlueButton>
        </Box>
      </Box>
    </DefaultDialog>
  );
};
