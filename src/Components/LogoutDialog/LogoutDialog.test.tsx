import {fireEvent, screen} from '@testing-library/react';
import {renderWithTheme} from 'TestHelper/TestHelper';
import {describe, expect, it, vi} from 'vitest';
import {LogoutDialog, LogoutDialogIcon} from './LogoutDialog';

describe('LogoutDialogIcon', () => {
  it('renders the logout icon correctly', () => {
    renderWithTheme(<LogoutDialogIcon />);
    const icon = screen.getByAltText('logout-icon');
    expect(icon).toBeInTheDocument();
  });
});

describe('LogoutDialog', () => {
  const defaultProps = {
    open: true,
    onClose: vi.fn(),
    onLogout: vi.fn(),
  };

  it('renders the dialog when open is true', () => {
    renderWithTheme(<LogoutDialog {...defaultProps} />);
    const logoutElements = screen.getAllByText('Logout');
    expect(logoutElements.length).toBeGreaterThan(0);
    expect(screen.getByText('Are you sure you want to log out of Repfabric?')).toBeInTheDocument();
    expect(screen.getByText("You'll need to sign in again to access your data.")).toBeInTheDocument();
  });

  it('does not render the dialog when open is false', () => {
    renderWithTheme(<LogoutDialog {...defaultProps} open={false} />);
    expect(screen.queryByText('Logout')).not.toBeInTheDocument();
  });

  it('renders action buttons correctly', () => {
    renderWithTheme(<LogoutDialog {...defaultProps} />);
    expect(screen.getByText('Stay Logged In')).toBeInTheDocument();
    expect(screen.getAllByText('Logout').length).toBeGreaterThan(0);
  });

  it('calls onClose when "Stay Logged In" button is clicked', () => {
    renderWithTheme(<LogoutDialog {...defaultProps} />);
    fireEvent.click(screen.getByText('Stay Logged In'));
    expect(defaultProps.onClose).toHaveBeenCalled();
  });

  it('calls onLogout when "Logout" button is clicked', async () => {
    renderWithTheme(<LogoutDialog {...defaultProps} />);
    const logoutButton = await screen.findByTestId('logout-button');
    fireEvent.click(logoutButton);
    expect(defaultProps.onLogout).toHaveBeenCalled();
  });

  it('calls onClose when dialog close button is clicked', () => {
    renderWithTheme(<LogoutDialog {...defaultProps} />);
    fireEvent.click(screen.getByTestId('close-button'));
    expect(defaultProps.onClose).toHaveBeenCalled();
  });
});
