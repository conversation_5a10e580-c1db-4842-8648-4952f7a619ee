import {Box} from '@mui/material';
import DateRangePicker from 'Components/DateRangePicker';
import {forwardRef, useEffect, useImperativeHandle, useState} from 'react';
import FilterChip from '../FilterChip/FilterChip';
import FilterSectionView from '../FilterChip/FilterSectionView';
import {IClearFilters} from '../FilterUtil/IClearFilters';
import {DateRangeUtil} from './DateRangeUtil';
import {SelectedDateRange} from './types';

/**
 * Enum representing the available date range options for filtering.
 * It includes options for the last month, last week, and a custom date range.
 */
export enum DateRangeOptions {
  LAST_MONTH = 'Last Month',
  LAST_WEEK = 'Last Week',
  CUSTOM = 'Custom',
}

/**
 * Interface representing the selected date range for the DateRangeFilter component.
 * It extends the SelectedDateRange interface and includes a dateRangeOption property.
 */
export interface DateRangeFilterSelectedDate extends SelectedDateRange {
  dateRangeOption: DateRangeOptions;
}

interface DateRangeFilterProps {
  value?: DateRangeFilterSelectedDate;
  sx?: React.CSSProperties;
  onSelect?: (selectedRange?: DateRangeFilterSelectedDate) => void;
}
type DateRangeFilterRef = IClearFilters;
/**
 * DateRangeFilter component that allows users to select a date range for filtering.
 * It provides options for last month, last week, and a custom date range.
 * @param {DateRangeFilterProps} props - The properties for the component.
 * @returns {JSX.Element} The rendered DateRangeFilter component.
 * @see DateRangeUtil for utility functions related to date ranges.
 */

export const DateRangeFilter = forwardRef<DateRangeFilterRef, DateRangeFilterProps>((props, ref) => {
  const [selectedRange, setSelectedRange] = useState<DateRangeOptions | null>(props.value?.dateRangeOption ?? null);
  const [showCustomDateRange, setShowCustomDateRange] = useState(
    props.value?.dateRangeOption === DateRangeOptions.CUSTOM,
  );

  useImperativeHandle(ref, () => ({
    clearSelection: () => {
      setSelectedRange(null);
      props.onSelect?.();
    },
  }));

  useEffect(() => {
    if (showCustomDateRange && props.value?.dateRangeOption !== DateRangeOptions.CUSTOM) {
      props.onSelect?.({
        startDate: DateRangeUtil.getStartOfDay(),
        endDate: DateRangeUtil.getEndOfDay(),
        dateRangeOption: DateRangeOptions.CUSTOM,
      });
    }
  }, [showCustomDateRange]);

  useEffect(() => {
    setShowCustomDateRange(false);
    switch (selectedRange) {
      case DateRangeOptions.LAST_MONTH:
        props.onSelect?.({
          ...DateRangeUtil.getLastMonthRange(),
          dateRangeOption: DateRangeOptions.LAST_MONTH,
        });
        break;
      case DateRangeOptions.LAST_WEEK:
        props.onSelect?.({
          ...DateRangeUtil.getLastWeekRange(),
          dateRangeOption: DateRangeOptions.LAST_WEEK,
        });
        break;
      case DateRangeOptions.CUSTOM:
        setShowCustomDateRange(true);
        break;
      default:
        props.onSelect?.(undefined);
    }
  }, [selectedRange]);

  return (
    <FilterSectionView title="Date" sx={props.sx}>
      <Box sx={{display: 'flex', flexDirection: 'row', gap: 0.75, flexWrap: 'wrap', textTransform: 'capitalize'}}>
        <FilterChip
          label={DateRangeOptions.LAST_MONTH}
          onClick={() => setSelectedRange(DateRangeOptions.LAST_MONTH)}
          selected={selectedRange === DateRangeOptions.LAST_MONTH}
          onDelete={() => setSelectedRange(null)}
        />
        <FilterChip
          label={DateRangeOptions.LAST_WEEK}
          onClick={() => setSelectedRange(DateRangeOptions.LAST_WEEK)}
          selected={selectedRange === DateRangeOptions.LAST_WEEK}
          onDelete={() => setSelectedRange(null)}
        />
        <FilterChip
          label={DateRangeOptions.CUSTOM}
          onClick={() => setSelectedRange(DateRangeOptions.CUSTOM)}
          selected={selectedRange === DateRangeOptions.CUSTOM}
          onDelete={() => setSelectedRange(null)}
        />
      </Box>
      {showCustomDateRange && (
        <DateRangePicker
          sx={{mt: 1}}
          onChange={value => {
            props.onSelect?.({
              startDate: value.from,
              endDate: value.to,
              dateRangeOption: DateRangeOptions.CUSTOM,
            });
          }}
          value={
            props.value && props.value.dateRangeOption === DateRangeOptions.CUSTOM
              ? {from: props.value.startDate, to: props.value.endDate}
              : null
          }
          disableFutureDates={false}
        />
      )}
    </FilterSectionView>
  );
});

DateRangeFilter.displayName = 'DateRangeFilter';

export default DateRangeFilter;
