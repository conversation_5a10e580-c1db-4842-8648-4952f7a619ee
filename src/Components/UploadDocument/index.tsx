import {Box, Grid, List, ListItem, Typography, useMediaQuery, useTheme} from '@mui/material';
import FileDropZone from 'Components/FileDropZone';
import FileViewCard from 'Components/FileViewCard';
import {PermissionsEnum} from 'Constants/enums';
import {fileExtensionsToUpload, handleDownload} from 'Helpers/utils';
import {useSnackbar} from 'notistack';
import React, {Dispatch, SetStateAction, useCallback, useEffect, useState} from 'react';
import {DropzoneOptions} from 'react-dropzone/.';
import {IFile} from 'types';
import Group from '../../Assets/Group.svg';

export const EmptyState = ({primaryText, isSmDown}: {primaryText: string; isSmDown: boolean}) => {
  return (
    <ListItem
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'column',
        width: '100%',
        height: '100%',
        padding: 0,
      }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Box
          component="img"
          src={Group}
          alt="file"
          sx={{
            width: isSmDown ? '6rem' : '10rem',
            height: isSmDown ? '6rem' : '10rem',
            objectFit: 'contain',
            marginBottom: 1,
          }}
        />
        <Typography
          sx={{
            textAlign: 'center',
            fontWeight: 600,
            fontSize: isSmDown ? 16 : 22,
            color: theme => theme.palette.text.secondary,
          }}
        >
          {primaryText}
        </Typography>
      </Box>
    </ListItem>
  );
};

/**
 * Props for the UploadDocuments component
 */
interface IProps {
  /** Instructional text shown when no files are uploaded */
  primaryText?: string;

  /** Callback when new files are uploaded */
  onUpload: (files: File[]) => void;

  /** Callback when a file is removed */
  onRemoveFile: (files: File[]) => void;

  /** List of currently selected/uploaded files */
  files?: File[];

  /** Additional options for the Dropzone */
  dropzoneProps?: DropzoneOptions;

  /** Whether to reset acceptedFiles when `files` prop changes */
  enableReinitialize?: boolean;

  /** List of previously uploaded (existing) files */
  existingFiles?: IFile[];

  /** Setter function to update existing files when a file is removed */
  onRemoveExistingFile?: Dispatch<SetStateAction<IFile[]>>;

  /** Permissions required to cancel/delete existing files */
  cancelPermissions?: PermissionsEnum[];
}

/**
 * A component to upload documents with drag-and-drop, list view of uploaded files,
 * and support for managing both new and existing files.
 *
 * @param props - Props to configure upload and file display behavior
 * @returns A file upload and viewer component
 */
const UploadDocuments: React.FC<IProps> = props => {
  const {
    primaryText = '1. Please upload a signed copy to further the process.',
    onRemoveFile,
    onUpload,
    files,
    dropzoneProps,
    enableReinitialize,
    existingFiles,
    onRemoveExistingFile,
    cancelPermissions,
  } = props;

  const {enqueueSnackbar} = useSnackbar();
  const [acceptedFiles, setAcceptedFiles] = useState<File[]>(files || []);

  const theme = useTheme();
  const isSmDown = useMediaQuery(theme.breakpoints.down('sm'));

  /**
   * Reinitialize acceptedFiles if `enableReinitialize` is true and `files` change
   */
  useEffect(() => {
    if (enableReinitialize && files?.[0]?.name) {
      setAcceptedFiles(files);
    } else if (!files?.length && enableReinitialize) {
      setAcceptedFiles([]);
    }
  }, [enableReinitialize, files]);

  /**
   * Handles the drop event from FileDropZone
   * Checks file count limits and updates state accordingly
   *
   * @param filesArg - Array of files dropped/uploaded
   */
  const handleFileDrop = useCallback(
    (filesArg: File[]) => {
      const maxFiles = dropzoneProps?.maxFiles;
      if (maxFiles !== undefined) {
        const totalFiles = acceptedFiles.length + filesArg.length;
        if (totalFiles > maxFiles) {
          enqueueSnackbar(`You can only upload up to ${maxFiles} files.`, {
            variant: 'error',
          });
          return;
        }
      }
      setAcceptedFiles(prev => [...prev, ...filesArg]);
      onUpload(filesArg);
    },
    [dropzoneProps?.maxFiles, onUpload, acceptedFiles, enqueueSnackbar],
  );

  /**
   * Removes a file from the acceptedFiles list and updates state
   *
   * @param fileName - Name of the file to remove
   * @param index - Index of the file in acceptedFiles
   */
  const handleFileRemove = (fileName: string, index: number) => {
    const newFiles = [...acceptedFiles];
    newFiles.splice(index, 1);
    setAcceptedFiles(newFiles);
    onRemoveFile(newFiles);
  };

  /**
   * Removes a file from existingFiles and updates parent via onRemoveExistingFile
   *
   * @param index - Index of the file in existingFiles
   */
  const handleExistingFileRemove = (index: number) => {
    if (existingFiles && onRemoveExistingFile) {
      const newFiles = [...existingFiles];
      newFiles.splice(index, 1);
      onRemoveExistingFile(newFiles);
    }
  };

  const textColor = 'secondary.linkBreadcrumb';

  return (
    <Grid
      container
      spacing={2}
      alignItems="stretch"
      sx={{
        height: {xs: 'auto', sm: '33.2825rem'},
        boxSizing: 'border-box',
        padding: '0.625rem',
      }}
    >
      {/* Upload section */}
      <Grid size={{xs: 12, sm: 5}} sx={{display: 'flex', flexDirection: 'column', height: '33.9375rem'}}>
        <Typography
          sx={{
            fontWeight: 'bold',
            fontSize: 14,
            color: textColor,
            paddingBottom: 1,
          }}
        >
          Upload documents
        </Typography>
        <FileDropZone
          onDrop={handleFileDrop}
          accept={fileExtensionsToUpload}
          dropzoneProps={dropzoneProps}
          files={acceptedFiles ?? []}
        />
      </Grid>

      {/* Attached files section */}
      <Grid size={{xs: 12, sm: 7}} sx={{display: 'flex', flexDirection: 'column', height: '33.9375rem'}}>
        <Typography
          sx={{
            fontWeight: 'bold',
            fontSize: 14,
            color: textColor,
            paddingBottom: 1,
          }}
        >
          Attached Files ({acceptedFiles.length})
        </Typography>

        <List
          sx={{
            color: textColor,
            flexGrow: 1,
            border: 1,
            borderRadius: 1,
            borderColor: 'body.100',
            display: 'flex',
            justifyContent: !acceptedFiles.length ? 'center' : 'flex-start',
            alignItems: !acceptedFiles.length ? 'center' : 'stretch',
            minHeight: '18rem',
            position: 'relative',
            overflow: 'hidden',
          }}
        >
          {/* Empty state */}
          {!acceptedFiles.length &&
            EmptyState({
              primaryText,
              isSmDown,
            })}

          {/* Files list */}
          {(acceptedFiles.length > 0 || existingFiles?.length) && (
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                maxHeight: '30rem',
                overflowY: 'auto',
                width: '100%',
              }}
            >
              {acceptedFiles.map((file, index) => (
                <ListItem key={file.name}>
                  <FileViewCard fileDetail={file} handleRemoveFile={() => handleFileRemove(file.name, index)} />
                </ListItem>
              ))}

              {existingFiles?.map((file, index) => (
                <ListItem key={file.id ?? file.originalName ?? index}>
                  <FileViewCard
                    fileDetail={file}
                    cancelPermissions={cancelPermissions}
                    handleRemoveFile={() => handleExistingFileRemove(index)}
                    handleDownload={() => handleDownload(file.url, file.originalName, '_blank')}
                  />
                </ListItem>
              ))}
            </Box>
          )}
        </List>
      </Grid>
    </Grid>
  );
};

export default UploadDocuments;
