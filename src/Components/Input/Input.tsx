import FileCopyIcon from '@mui/icons-material/FileCopy';
import ReportProblemOutlinedIcon from '@mui/icons-material/ReportProblemOutlined';
import {
  FormControl,
  FormHelperText,
  IconButton,
  OutlinedInput,
  OutlinedInputProps,
  SxProps,
  Theme,
  Tooltip,
} from '@mui/material';
import InputLabel from 'Components/InputLabel';
import React, {memo, useCallback, useState} from 'react';

/**
 * Represents an error object with arbitrary keys and values.
 */
interface AnyErrorObj {
  [key: string]: unknown;
}

/**
 * Props for the `Input` component.
 */
export type InputProps = Omit<OutlinedInputProps, 'onChange' | 'onBlur' | 'onFocus'> & {
  /** Input element ID */
  id: string;

  /** Optional label for the input */
  label?: string;

  /** Enable copy-to-clipboard icon at end */
  copyEnabled?: boolean;

  /** Error message or error object to display */
  errorMessage?: string | AnyErrorObj;

  /** Start adornment for the input */
  startAdornment?: React.ReactNode;

  /** End adornment for the input */
  endAdornment?: React.ReactNode;

  /** Callback when input value changes */
  onChange?: (val: string) => void;

  /** Callback when input loses focus */
  onBlur?: () => void;

  /** Callback when input is focused */
  onFocus?: () => void;

  /** Helper text below the input */
  helperText?: string;

  /** Style overrides for MUI `sx` prop */
  sxProps?: SxProps<Theme>;

  /** Style overrides for label */
  labelSx?: object;

  /** Inline styles for input */
  inputSx?: React.CSSProperties;

  /** Inline styles for error helper text */
  errorSx?: React.CSSProperties;
};

/**
 * Generates the appropriate end adornment for the input field based on the input state.
 *
 * @param params - Configuration for adornment rendering.
 * @returns A React node representing the end adornment (icon or custom element).
 */
const getEndAdornment = ({
  copyEnabled,
  value,
  isError,
  endAdornment,
}: {
  copyEnabled: boolean;
  value?: OutlinedInputProps['value'];
  isError: boolean | undefined;
  endAdornment: React.ReactNode;
}) => {
  if (endAdornment && !isError) return endAdornment;
  if (isError) return <ReportProblemOutlinedIcon color="error" />;
  if (copyEnabled)
    return (
      <Tooltip title="Copy to clipboard">
        <IconButton sx={{cursor: 'pointer'}}>
          <FileCopyIcon />
        </IconButton>
      </Tooltip>
    );
};

const blackMain = 'black.main';

/**
 * A reusable MUI-based Input component that supports labels, error display,
 * helper text, adornments, and copy-to-clipboard functionality.
 *
 * @param props - {@link InputProps}
 * @returns A styled and configurable input field.
 */
const Input: React.FC<InputProps> = ({
  id,
  value,
  label,
  helperText,
  disabled = false,
  endAdornment,
  copyEnabled = false,
  errorMessage,
  onChange,
  labelSx,
  sxProps,
  inputSx = {},
  errorSx = {},
  ...rest
}) => {
  const [focused, setFocused] = useState(false);
  const isError = !!errorMessage;

  const handleChangeEvent = useCallback(
    (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      console.info('focused:', focused);
      if (onChange) onChange(e?.target?.value);
    },
    [onChange],
  );

  const handleBlur = () => {
    if (!value) {
      setFocused(false);
    }
  };

  return (
    <FormControl sx={{width: 1}} data-testid="inputFormControl" error={isError} disabled={disabled}>
      {label && (
        <InputLabel
          htmlFor={id}
          sx={{
            ...labelSx,
            color: blackMain,
            '&.Mui-focused': {color: blackMain},
            '&.MuiFormLabel-root': {color: blackMain},
            fontSize: 17,
          }}
        >
          {label}
        </InputLabel>
      )}

      <OutlinedInput
        disabled={disabled}
        data-testid="input"
        value={value}
        id={id}
        inputProps={{
          sx: {
            padding: 1,
            // ...inputSx, // Merge custom input styles (optional if needed)
          },
        }}
        onChange={handleChangeEvent}
        onBlur={handleBlur}
        endAdornment={getEndAdornment({copyEnabled, value, isError, endAdornment})}
        {...rest}
      />

      {(isError || helperText) && (
        <FormHelperText sx={{...errorSx}}>{isError ? <>{errorMessage}</> : helperText}</FormHelperText>
      )}
    </FormControl>
  );
};

export default memo(Input);
