import BillingInvoiceIcon from 'Assets/side-nav/side-nav-billing-invoice.svg';
import HomeOutlinedIcon from 'Assets/side-nav/side-nav-home.svg';
import ObservabilityIcon from 'Assets/side-nav/side-nav-observability.svg';
import PlansIcon from 'Assets/side-nav/side-nav-plans.svg';
import TenantIcon from 'Assets/side-nav/side-nav-teams.svg';
import UsermanagementIcon from 'Assets/side-nav/side-nav-user-mgmt.svg';
import SVGImageFromPath from 'Components/SVGImageFromPath';
import {ReactNode} from 'react';
import {RouteNames} from 'Routes/routeNames';

export interface SideNavDividerType {
  type: 'divider';
  visible: boolean;
}

export interface SideNavTitleType extends Omit<SideNavDividerType, 'type'> {
  label: string;
  type: 'title';
}

export interface SideNavConfig extends Omit<SideNavDividerType, 'type'>, Omit<SideNavTitleType, 'type'> {
  type?: 'title' | 'divider';
  link?: string;
  icon?: ReactNode;
  children?: (SideNavConfig | SideNavTitleType)[];
}
const sideNavConfig: SideNavConfig[] = [
  {
    label: 'Dashboard',
    link: RouteNames.HOME,
    icon: <SVGImageFromPath path={HomeOutlinedIcon} />,
    visible: true,
  },
  {
    label: 'Tenants',
    link: RouteNames.TENANTS,
    icon: <SVGImageFromPath path={TenantIcon} />,
    visible: true,
  },
  {
    label: 'Billing & Invoices',
    link: RouteNames.BILLING_INVOICES,
    icon: <SVGImageFromPath path={BillingInvoiceIcon} />,
    visible: true,
  },

  {
    label: 'Plans',
    link: RouteNames.PLANS,
    icon: <SVGImageFromPath path={PlansIcon} />,
    visible: true,
  },
  {
    label: 'Observability',
    link: RouteNames.OBSERVABILITY,
    icon: <SVGImageFromPath path={ObservabilityIcon} />,
    visible: true,
  },
  {
    label: 'Users & Roles',
    icon: <SVGImageFromPath path={UsermanagementIcon} />,
    visible: true,
    children: [
      {
        label: 'User Management',
        link: RouteNames.USER_MANAGEMENT,
        visible: true,
      },
      {
        label: 'Roles & Permissions',
        link: RouteNames.ROLES_RESPONSIBILITIES,
        visible: true,
      },
    ],
  },
];

export default sideNavConfig;
