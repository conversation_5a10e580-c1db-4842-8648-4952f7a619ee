import {Chip} from '@mui/material';
import {ClearIcon} from '@mui/x-date-pickers';

export interface FilterChipProps {
  label: string;
  selected?: boolean;
  onClick?: () => void;
  onDelete?: () => void;
  sx?: React.CSSProperties;
}

const secondary50 = 'secondary.50';
const whiteMain = 'white.main';
const secondaryMain = 'secondary.main';

const FilterChip: React.FC<FilterChipProps> = ({label, onClick, sx, selected = false, onDelete}) => {
  const fontSize = '0.75rem';
  const backColor = selected ? secondary50 : whiteMain;
  return (
    <Chip
      label={label}
      onClick={onClick}
      variant="outlined"
      onDelete={selected ? onDelete : undefined}
      deleteIcon={<ClearIcon sx={{p: 0.3, color: 'red'}} />}
      sx={{
        backgroundColor: selected ? secondary50 : whiteMain,
        color: !selected ? 'body.600' : secondaryMain,
        fontWeight: 700,
        fontSize: fontSize,
        borderColor: !selected ? secondary50 : secondaryMain,
        borderWidth: 1,
        cursor: 'pointer',
        '&.MuiChip-clickable:hover': {
          backgroundColor: backColor,
        },
        '& .MuiChip-deleteIcon': {
          color: secondaryMain,
          '&:hover': {
            color: 'secondary.500',
          },
        },
        ...sx,
      }}
    />
  );
};

export default FilterChip;
