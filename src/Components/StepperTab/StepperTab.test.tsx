import {render, screen} from '@testing-library/react';
import {describe, expect, it} from 'vitest';
import StepperTab, {StepperTabProps} from './StepperTab';

const defaultSteps = ['Step 1', 'Step 2', 'Step 3'];

describe('StepperTab', () => {
  const renderComponent = (props?: Partial<StepperTabProps>) =>
    render(<StepperTab steps={defaultSteps} activeStep={1} {...props} />);

  it('renders all step labels correctly', () => {
    renderComponent();

    defaultSteps.forEach(step => {
      expect(screen.getByText(step)).toBeInTheDocument();
    });
  });

  it('renders no steps if steps array is empty', () => {
    render(<StepperTab steps={[]} activeStep={0} />);
    expect(screen.queryAllByRole('listitem')).toHaveLength(0);
  });

  it('renders stepper with memoization (no crash)', () => {
    const {container, rerender} = render(<StepperTab steps={['Step A', 'Step B']} activeStep={0} />);
    expect(container).toBeInTheDocument();

    // re-render with same props to ensure memoization doesn't break
    rerender(<StepperTab steps={['Step A', 'Step B']} activeStep={0} />);
    expect(screen.getByText('Step A')).toBeInTheDocument();
  });
});
