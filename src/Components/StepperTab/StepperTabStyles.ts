import {SxProps, Theme} from '@mui/material';
import {Integers} from 'Helpers/integers';

interface StepperTabStyles {
  stepper: SxProps<Theme>;
}

const whiteMain = 'white.main';
const primary50 = 'primary.50';
const primaryMain = 'primary.main';

export const StepperTabStyles: StepperTabStyles = {
  stepper: {
    backgroundColor: primary50,
    paddingTop: '0.75rem', // 12px
    paddingBottom: '0.75rem',
    paddingLeft: '12.5rem', // maintain wide layout
    paddingRight: '12.5rem',
    borderBottom: theme => `0.0625rem solid ${theme.palette.primary[Integers.OneHundredFifty]}`, // 1px solid primary.200
    minHeight: '3.5rem', // 56px
    opacity: 1,

    '& .MuiStepConnector-root': {
      top: '50%',
      left: 'calc(-50% + 1.25rem)',
      right: 'calc(50% + 1.25rem)',
    },

    '& .MuiStepConnector-line': {
      borderColor: theme => theme.palette.primary[Integers.TwoHundred],
      borderWidth: '0.0625rem',
    },

    '& .MuiStepIcon-root': {
      width: '2rem', // 32px
      height: '2rem',
      color: whiteMain,
      border: theme => `0.0625rem solid ${theme.palette.primary.main}`,
      borderRadius: '50%',
      backgroundColor: whiteMain,

      '& text': {
        fill: theme => theme.palette.primary.main,
      },
      '&.Mui-active': {
        color: whiteMain,
        border: theme => `0.0625rem solid ${theme.palette.primary.main}`,
        backgroundColor: primary50,
        '& text': {
          fill: theme => theme.palette.primary.main,
        },
      },
      '&.Mui-completed': {
        color: primaryMain,
        border: theme => `0.0625rem solid ${theme.palette.primary.main}`,
        backgroundColor: primary50,
      },
    },

    '& .MuiStepIcon-text': {
      fontSize: '0.875rem', // 14px
    },

    '& .MuiStepLabel-label': {
      color: primaryMain,
      fontSize: '0.9375rem', // 15px
      fontWeight: '700',

      '&.Mui-active': {
        color: primaryMain,
        fontWeight: '700',
      },
      '&.Mui-completed': {
        color: primaryMain,
      },
    },
  },
};
