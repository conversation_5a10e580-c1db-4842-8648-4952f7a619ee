import {useTheme} from '@mui/material';
import {Integers} from 'Helpers/integers';
import React from 'react';
import {Link} from 'react-router-dom';
/**
 * Represents an individual breadcrumb item.
 */
export interface BreadcrumbItem {
  label: string;
  url?: string;
}

interface BreadcrumbProps {
  /**
   * Array of breadcrumb items to display.
   */
  items?: BreadcrumbItem[];

  /**
   * Separator string between items. Defaults to '|'.
   */
  separator?: string;

  /**
   * Whether to show the last breadcrumb label as a heading.
   */
  showHeader?: boolean;
}

/**
 * Breadcrumb component renders a breadcrumb navigation trail.
 *
 * @component
 * @param {BreadcrumbProps} props - Props to configure breadcrumb.
 * @returns {JSX.Element} Rendered breadcrumb navigation.
 */
const Breadcrumb: React.FC<BreadcrumbProps> = ({items = [], separator = '|', showHeader = false}) => {
  const lastLabel = items.length ? items[items.length - 1].label : '';
  const theme = useTheme();
  const color = theme.palette;

  return (
    <div style={{display: 'flex', flexDirection: 'column'}}>
      {showHeader && (
        <h2
          style={{
            margin: '0',
            fontFamily: 'Lato, sans-serif',
            fontWeight: 700,
            fontStyle: 'normal',
            fontSize: '1.125rem', // 18px = 1.125rem
            lineHeight: '1.625rem', // 26px = 1.625rem
            letterSpacing: '0',
            verticalAlign: 'middle',
          }}
        >
          {lastLabel}
        </h2>
      )}

      <nav style={{display: 'flex', alignItems: 'center', fontSize: '0.625rem', marginBottom: '0.375rem'}}>
        {items.map((item, index) => {
          const isLast = index === items.length - 1;
          return (
            <React.Fragment key={item.label}>
              {!isLast ? (
                <>
                  {item.url ? (
                    <Link
                      to={item.url}
                      style={{
                        textDecoration: 'none',
                        color: color.body[Integers.FiveHundred],
                        marginRight: '0.5rem',
                        paddingBottom: '0.125rem',
                        borderBottom: `0.0625rem solid ${color.body[Integers.ThreeHundred]}`,
                        height: '0.875rem', // 14px = 0.875rem
                        transform: 'rotate(0deg)',
                        opacity: 1,
                        display: 'inline-flex',
                        alignItems: 'center',
                        gap: '0.625rem', // 10px = 0.625rem
                      }}
                    >
                      {item.label}
                    </Link>
                  ) : (
                    <span
                      style={{
                        color: color.body[Integers.FiveHundred],
                        marginRight: '0.5rem',
                        paddingBottom: '0.125rem',
                        borderBottom: `0.0625rem solid ${color.body[Integers.ThreeHundred]}`,
                      }}
                    >
                      {item.label}
                    </span>
                  )}
                  <span style={{color: color.body[Integers.ThreeHundred], margin: '0 0.5rem'}}>{separator}</span>
                </>
              ) : (
                <span
                  style={{
                    fontWeight: 'bold',
                    color: color.black.main,
                    paddingBottom: '0.125rem',
                    borderBottom: `0.0625rem solid ${color.black.main}`,
                  }}
                >
                  {item.label}
                </span>
              )}
            </React.Fragment>
          );
        })}
      </nav>
    </div>
  );
};

export default Breadcrumb;
