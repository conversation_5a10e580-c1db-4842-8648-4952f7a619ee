import { PaletteOptions } from '@mui/material';

type ShrinkPaletteOption = Omit<PaletteOptions, 'mode'>;
export const paletteConfig: {
  light: ShrinkPaletteOption;
  dark: ShrinkPaletteOption;
} = {
  light: {
    background: {
      default: '#ffffff',
    },
    primary: {
      main: '#852352',
      50: '#F7ECF1',
      100: '#E9C1CF',
      150: '#E9C1CF',
      200: '#CBA0AF',
      300: '#AE8091',
      400: '#916173',
      500: '#754457',
      600: '#59273C',
      700: '#3E0923',
      800: '#21000D',
      900: '#030000',
    },
    secondary: {
      main: '#173478',
      25: '#F3F3FA',
      50: '#F0F0FB',
      100: '#C0CEEA',
      200: '#9FAECD',
      300: '#7F8FB0',
      400: '#607294',
      500: '#435579',
      600: '#28395e',
      650: '#0E2762',
      700: '#0E1F45',
      800: '#00052C',
      900: '#00000A',
    },
    text: {
      secondary: '#6B6B6B',
      primary: '#2C2C2C',
    },
    divider: '#e0e0e0',
    table: {
      header: '#EFEFF2',
    },
    body: {
      dark: '#0A0A0A', // Light gray for body background
      50: '#F1F1F1',
      100: '#DBDBDB',
      200: '#BABABA',
      300: '#9D9D9D',
      350: '#999999',
      400: '#838383',
      500: '#6B6B6B',
      600: '#545454',
      700: '#3F3F3F',
      800: '#2C2C2C',
      900: '#1A1A1A',
    },

    white: {
      main: '#ffffff',
      100: '#F9F9F9',
      200: '#F0F0FB',
      300: '#DBDBDB',
    },
    black: {
      main: '#000000',
    },
    sideNav: {
      active: '#173478',
      hover: '#F7F7F7',
      linkTextActive: '#435579',
    },
    alert: {
      success: {
        main: '#1AC371',
        bg: '#E2FFF1',
        onBg: '#0D653B',
        border: '#80B29A',
      },
      error: {
        main: '#FF1818',
        bg: '#FFE0E0',
        onBg: '#A91417',
        border: '#E8ABAC',
      },
      warning: {
        main: '#E7BF1B',
        bg: '#FFF9E0',
        onBg: '#736116',
        border: '#E9E0BD',
      },
      info: {
        main: '#4278F8',
        bg: '#F0F0FB',
        onBg: '#17377F',
        border: '#CECEE8',
      },
    },

    tenantStatus: {
      failed: {
        bg: '#FFE7DC',
        indicator: '#FF7E42',
        onBg: '#79310F',
      },
      provisioning: {
        bg: '#F3ECE4',
        indicator: '#9D815F',
        onBg: '#483014',
      },
    },
    plan: {
      normal: {
        1: '#C2D4FF',
        2: '#FFF2F8',
      },
      selected: {
        1: '#AAC4FF',
        2: '#FFE8F3',
      },
    },
  },
  dark: {
    text: {
      primary: '#f8fafc',
      secondary: '#cbd5e1',
    },
    divider: '#374151',
    border: {
      main: '#ffffff1f',
    },
  },
};

const MUIPickerDefaultStyle = {
  backgroundColor: 'secondary.main', // selected day color
  color: 'white.main', // selected day text color
  fontWeight: 600,
};

export const commonConfig = {
  typography: {
    fontFamily: ['Lato', 'Inter', 'Roboto', 'Arial', 'sans-serif'].join(','),
    fontSize: 16,
    h1: {
      fontSize: '2.5rem',
      fontWeight: 700,
      lineHeight: 1.2,
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 600,
      lineHeight: 1.3,
    },
    h3: {
      fontSize: '1.75rem',
      fontWeight: 600,
      lineHeight: 1.3,
    },
    h4: {
      fontSize: '1.5rem',
      fontWeight: 600,
      lineHeight: 1.4,
    },
    h5: {
      fontSize: '1.25rem',
      fontWeight: 500,
      lineHeight: 1.4,
    },
    h6: {
      fontSize: '1.125rem',
      fontWeight: 500,
      lineHeight: 1.4,
    },
    body1: {
      fontSize: '1rem',
      lineHeight: 1.6,
    },
    body2: {
      fontSize: '0.875rem',
      lineHeight: 1.5,
    },
    button: {
      textTransform: 'none' as const,
      fontWeight: 500,
    },
  },
  shape: {
    borderRadius: '0.5rem', // 8px converted to rem (8/16 = 0.5)
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        '*::-webkit-scrollbar': {
          height: '0.5rem', // 8px converted to rem
          width: '0.5rem',
        },
        '*::-webkit-scrollbar-track': {
          background: '#fafafa',
        },
        '*::-webkit-scrollbar-thumb': {
          backgroundColor: '#d1d1d1',
          borderRadius: '6.25rem', // 100px converted to rem
          opacity: '0.5',
        },
        '*::-webkit-scrollbar-thumb:horizontal': {
          width: '0.5rem', // 8px converted to rem
          height: '18.75rem', // 300px converted to rem
          backgroundColor: '#d1d1d1',
          backgroundClip: 'padding-box',
          borderRight: '1rem white solid', // 16px converted to rem
        },
      },
    },
    MuiPickersDay: {
      styleOverrides: {
        root: {
          color: 'body.dark',
          '&.Mui-selected:focus': MUIPickerDefaultStyle,
          '&.Mui-selected': MUIPickerDefaultStyle,
          '&.Mui-selected:hover': {
            fontWeight: 600,
            backgroundColor: 'secondary.500',
          },
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        sizeLarge: {
          minHeight: '3.125rem', // 50px converted to rem
          p: 0,
        },
        root: {
          borderRadius: '0.5rem', // 8px converted to rem
          padding: '0.75rem 1.5rem', // 12px 24px converted to rem
          fontSize: '0.9rem',
          fontWeight: 500,
          textTransform: 'none' as const,
          boxShadow: 'none',
          '&:hover': {
            boxShadow: '0 0.25rem 0.5rem #00000020', // rgba converted to hex with opacity
          },
        },
        contained: {
          '&:hover': {
            boxShadow: '0 0.25rem 0.75rem #00000026', // rgba converted to hex with opacity
          },
        },
      },
    },
    MuiOutlinedInput: {
      styleOverrides: {
        root: {
          borderRadius: '0.5rem', // 8px converted to rem
          '&:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: '#d1d5db',
          },
          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
            borderWidth: '0.125rem', // 2px converted to rem
          },
        },
      },
    },
    MuiFormLabel: {
      styleOverrides: {
        root: {
          fontSize: '0.875rem',
          fontWeight: 500,
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: '0.75rem', // 12px converted to rem
          boxShadow: '0 0.0625rem 0.1875rem #0000001a', // rgba converted to hex with opacity
        },
      },
    },
  },
};
