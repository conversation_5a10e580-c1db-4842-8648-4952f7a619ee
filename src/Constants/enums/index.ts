import dayjs from 'dayjs';

export * from './permissions';

// Enum identifying different API service slices used in the application
export enum ApiSliceIdentifier {
  TENANT_FACADE, // Provides an interface for tenant-related operations
  AUDIT_FACADE, // Provides an interface for audit-related operations
}

export const REGISTERED_DOMAIN = '.repfabric.com';

/**
 * Enum identifying different options for table rows per page.
 */
export enum TableRowsPerPage {
  Small = 10,
  Medium = 25,
  Large = 50,
  XLarge = 100,
}

/**
 * default Table rows per page options for pagination dropdown
 */
export const defaultTableRowsPerPageArr = Object.values(TableRowsPerPage) as number[];

export const defaultTableDateFormatting = (date: Date | number | string) => {
  try {
    const d = new Date(date);
    const formattedDate = dayjs(d).format('DD MMM, YYYY');
    return formattedDate;
  } catch {
    return '';
  }
};
