import AddPlan from 'Pages/PlansPage/AddPlan';
import AddTenant from 'Pages/Tenants/AddTenant/AddTenantPage';
import {lazy} from 'react';
import {RouteNames} from 'Routes/routeNames';

const Home = lazy(() => import('Pages/Home'));
const TenantPage = lazy(() => import('Pages/Tenants/TenantPage'));
const TenantDetailsPage = lazy(() => import('Pages/Tenants/TenantDetails/TenantDetailsPage'));
const EditPlan = lazy(() => import('Pages/PlansPage/EditPlan'));
const mainLayoutRouteConfig = [
  {
    path: RouteNames.HOME,
    component: Home,
  },
  {
    path: RouteNames.TENANTS,
    component: TenantPage,
  },
  {
    path: RouteNames.TENANT_DETAILS,
    component: TenantDetailsPage,
  },
  {
    path: RouteNames.ADD_TENANT,
    component: AddTenant,
  },
  {
    path: RouteNames.ADD_PLANS,
    component: AddPlan,
  },
  {
    path: RouteNames.BILLING_INVOICES,
    component: lazy(() => import('Pages/BillingInvoicePage/BillingInvoicePage')),
  },
  {
    path: RouteNames.PLANS,
    component: lazy(() => import('Pages/PlansPage/PlanPage')),
  },
  {
    path: RouteNames.OBSERVABILITY,
    component: lazy(() => import('Pages/ObservabilityPage/ObservabilityPage')),
  },
  {
    path: RouteNames.USER_MANAGEMENT,
    component: lazy(() => import('Pages/UserManagementPage/UserManagementPage')),
  },
  {
    path: RouteNames.ROLES_RESPONSIBILITIES,
    component: lazy(() => import('Pages/RolesResponsibilitiesPage/RolesResponsibilitiesPage')),
  },
  {
    path: '/plans/:id/edit',
    component: EditPlan,
  },
];

export default mainLayoutRouteConfig;
