import {UserViewType} from 'redux/app/types';
import {apiSlice} from '../apiSlice';
import {ICommonApiFilterDTO} from './commonApi.type';
import {User} from './user.model';

/**
 * Interface for login form credentials.
 */
export interface ILoginForm {
  username: string;
  password: string;
}

/**
 * Interface for token retrieval.
 */
export interface IToken {
  code: string;
}

/**
 * Interface for forgot password request.
 */
export interface IForgotPassword {
  email: string;
}

/**
 * Interface for reset password request.
 */
export interface IResetPassword {
  newPassword: string;
  token: string;
}

export interface ITenantRoleByUser {
  roleName: string;
  roleId: string;
  userCount: number;
  status: number;
  tenantId: string;
  createdOn: string;
  modifiedOn: string;
}

/**
 * RTK Query API slice for authentication and user management endpoints.
 * Injects endpoints for login, logout, token, password management, and tenant user queries.
 */
export const authApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    /**
     * Login mutation - authenticates user with credentials.
     */
    login: builder.mutation({
      query: (credentials: ILoginForm) => ({
        url: '/auth/login',
        method: 'POST',
        body: {...credentials},
      }),
    }),
    /**
     * Token mutation - retrieves a token using a code.
     */
    getToken: builder.mutation({
      query: (credentials: IToken) => ({
        url: '/auth/token',
        method: 'POST',
        body: {...credentials},
      }),
    }),
    /**
     * Forgot password mutation - sends a password reset email.
     */
    forgotPassword: builder.mutation({
      query: (credentials: IForgotPassword) => ({
        url: '/auth/forget-password',
        method: 'POST',
        body: {...credentials},
      }),
    }),
    /**
     * Reset password mutation - resets password using a token.
     */
    resetPassword: builder.mutation({
      query: (credentials: IResetPassword) => ({
        url: '/auth/forget-password/verify',
        method: 'POST',
        body: {newPassword: credentials.newPassword},
        headers: {
          Authorization: `Bearer ${credentials.token}`,
        },
      }),
    }),
    /**
     * Logout mutation - logs out the user and invalidates the refresh token.
     */
    logout: builder.mutation({
      query: (refreshToken: string | null) => ({
        url: '/auth/logout',
        method: 'POST',
        body: {refreshToken},
      }),
    }),
    /**
     * Query to get the current authenticated user.
     */
    getUser: builder.query<User, void>({
      query: () => ({url: '/auth/me'}),
    }),
    getRolesByGroup: builder.query<ITenantRoleByUser[], ICommonApiFilterDTO>({
      query: filter => {
        return {
          url: `/tenants/role-view`,
          method: 'GET',
          params: {
            filter: JSON.stringify({
              ...filter,
              where: filter.where ? JSON.stringify(filter.where) : undefined,
            }),
          },
        };
      },
    }),
    getCountOfRolesByGroup: builder.query<{count: number}, ICommonApiFilterDTO>({
      query: filter => ({
        url: `/tenants/role-view/count`,
        method: 'GET',
        params: {
          where: filter.where ? JSON.stringify(filter.where) : undefined,
        },
      }),
    }),
    /**
     * Query to get tenant users with optional offset/filter.
     * This endpoint is used to retrieve a list of users belonging to a specific tenant.
     */
    getUserLists: builder.query<UserViewType[], ICommonApiFilterDTO>({
      query: params => ({
        url: `/users/user-list`,
        method: 'GET',
        params: {
          filter: JSON.stringify(params),
        },
      }),
    }),
    /**
     * Query to get the count of tenant users.
     * This endpoint is used to retrieve the total number of users in a tenant.
     */
    getUsersCount: builder.query<{count: number}, string>({
      query: userTenantId => ({
        url: '/users/count',
        method: 'GET',
        params: {
          where: JSON.stringify({
            userTenantId: {
              neq: userTenantId,
            },
          }),
        },
      }),
    }),
  }),
});

/**
 * Exported RTK Query hooks for authentication and user management.
 */
export const {
  useLoginMutation,
  useLogoutMutation,
  useGetTokenMutation,
  useGetUserQuery,
  useForgotPasswordMutation,
  useResetPasswordMutation,
  useGetRolesByGroupQuery,
  useLazyGetRolesByGroupQuery,
  useGetCountOfRolesByGroupQuery,
  useLazyGetCountOfRolesByGroupQuery,
  useGetUserListsQuery,
  useLazyGetUserListsQuery,
  useGetUsersCountQuery,
  useLazyGetUsersCountQuery,
} = authApiSlice;
