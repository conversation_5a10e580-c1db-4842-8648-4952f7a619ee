import {ApiSliceIdentifier} from 'Constants/enums';
import {FormAddPlan} from 'Pages/PlansPage/AddPlan';
import {apiSlice} from 'redux/apiSlice';
import {ITenantStatus, TenantApiDTO, TenantApiForCountDTO, TenantFilterDTO, TenantType, UserViewType} from './types';
import {PlanType} from './types/plan.type';

const apiSliceIdentifier = ApiSliceIdentifier.TENANT_FACADE;

/**
 * Composes a tenant filter object from the provided filter query.
 *
 * @param filterQuery The filter criteria for querying tenants.
 * It can include properties like limit, offset, order, status, dateRange, and searchValue.
 * @returns A TenantFilterDTO object that can be used to filter tenant queries.
 * If no filterQuery is provided, it returns an empty object.
 */
const composeTenantFilter = (filterQuery: TenantApiDTO): TenantFilterDTO => {
  if (Object.keys(filterQuery).length === 0) return {};
  let filter: TenantFilterDTO = {};
  if (filterQuery.limit) {
    filter.limit = filterQuery.limit;
  }
  if (filterQuery.offset) {
    filter.offset = filterQuery.offset;
  }
  if (filterQuery.order) {
    filter.order = filterQuery.order;
  }

  if (filterQuery.status && filterQuery.status.length > 0) {
    filter.where = {
      status: {
        inq: filterQuery.status,
      },
    };
  }

  if (filterQuery.dateRange) {
    filter.where = {
      ...filter.where,
      createdOn: {
        between: [filterQuery.dateRange.startDate.toISOString(), filterQuery.dateRange.endDate.toISOString()],
      },
    };
  }

  if (filterQuery.searchValue && filterQuery.searchValue.trim().length > 0) {
    filter.where = {
      ...filter.where,
      name: {
        ilike: `%${filterQuery.searchValue}%`,
      },
    };
  }

  return filter;
};

/**
 * Injects tenant management related endpoints into the base API slice.
 *
 * @remarks
 * This slice provides endpoints for managing tenants, including fetching tenants,
 * counting tenants, verifying tenant keys, and creating new tenants.
 *
 * @example
 * // Usage in a component
 * const { data, error } = useGetTenantsQuery(filterQuery);
 *
 * @see {@link apiSlice}
 *
 * @endpoint getTenants
 * Fetches a list of tenants based on the provided filter query.
 * @param filterQuery - The filter criteria for querying tenants.
 *
 * @endpoint getTenantsCount
 * Retrieves the count of tenants matching the provided filter query.
 * @param filterQuery - (Optional) The filter criteria for counting tenants.
 *
 * @endpoint verifyTenantKey
 * Verifies a tenant key.
 * @param KeyDto - The key data transfer object to verify.
 *
 * @endpoint createTenant
 * Creates a new tenant.
 * @param formData - The form data containing tenant details.
 */
export const tenantApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getTenants: builder.query({
      query: (filterQuery: TenantApiDTO) => {
        const filter = composeTenantFilter(filterQuery);
        return {
          url: '/tenants',
          method: 'GET',
          params: {
            filter: JSON.stringify(filter),
          },
          apiSliceIdentifier,
        };
      },
    }),
    getTenantsCount: builder.query({
      query: (filterQuery: TenantApiForCountDTO = {}) => {
        const filter = composeTenantFilter(filterQuery);
        return {
          url: '/tenants/count',
          method: 'GET',
          params: filter?.where && {
            where: JSON.stringify({
              ...filter.where,
            }),
          },
          apiSliceIdentifier,
        };
      },
    }),
    verifyTenantKey: builder.mutation({
      query: KeyDto => ({
        url: '/tenants/verify-key',
        method: 'POST',
        body: KeyDto,
        apiSliceIdentifier,
      }),
    }),
    createTenant: builder.mutation<TenantType, FormData>({
      query: formData => ({
        url: '/tenants',
        method: 'POST',
        apiSliceIdentifier,
        body: formData,
      }),
    }),
    getAllTenantStatuses: builder.query<ITenantStatus, void>({
      query: () => ({
        url: '/tenants/all-statuses',
        method: 'GET',
        apiSliceIdentifier,
      }),
    }),
    getTenantById: builder.query<TenantType, string>({
      query: (tenantId: string) => ({
        url: `/tenants/${tenantId}`,
        method: 'GET',
        apiSliceIdentifier,
      }),
    }),
    getPlanById: builder.query<FormAddPlan, {planId: string; filter?: object}>({
      query: ({planId, filter}) => {
        const params: Record<string, string> = {};

        if (filter) {
          params.filter = JSON.stringify(filter); // LoopBack expects filter as a JSON string
        }

        return {
          url: `/plans/${planId}`,
          method: 'GET',
          params, // <-- automatically converted into query string
          apiSliceIdentifier,
        };
      },
    }),
    updatePlanById: builder.mutation<TenantType, {planID: string; data: Partial<PlanType>}>({
      query: ({planID, data}) => ({
        url: `/plans/${planID}`,
        method: 'PATCH',
        body: data,
        apiSliceIdentifier,
      }),
    }),

    getUserById: builder.query<UserViewType[], string>({
      query: id => {
        const filter = {where: {userTenantId: id}};
        return {
          url: '/users',
          method: 'GET',
          params: {filter: JSON.stringify(filter)},
        };
      },
    }),
  }),
});

export const {
  useVerifyTenantKeyMutation,
  useGetTenantsQuery,
  useGetTenantsCountQuery,
  useLazyGetTenantsCountQuery,
  useLazyGetTenantsQuery,
  useCreateTenantMutation,
  useGetAllTenantStatusesQuery,
  useGetTenantByIdQuery,
  useLazyGetTenantByIdQuery,
  useGetPlanByIdQuery,
  useUpdatePlanByIdMutation,
  useGetUserByIdQuery,
} = tenantApiSlice;
