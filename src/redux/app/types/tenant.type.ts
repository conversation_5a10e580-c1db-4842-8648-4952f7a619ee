import {SelectedDateRange} from 'Components/DateRangeFilter/types';
import {ContactType} from './contact.type';
import {ResourceType} from './resource.type';
import {Subscription} from './subscription.type';

export interface IFile {
  [x: string]: string | number | undefined;
  id: string;
  tenantId: string;
  fileKey: string;
  originalName: string;
  source: number | string;
  size: number;
  url: string;
  signedUrl?: string; // Signed URL for secure access
}
// Enum representing different statuses of a tenant coming from the API
export enum TenantStatus {
  ACTIVE, // Tenant is active and fully functional
  PENDINGPROVISION, // Tenant is awaiting provisioning
  PROVISIONING, // Tenant is currently being provisioned
  PROVISIONFAILED, // Provisioning process failed
  DEPROVISIONING, // Tenant is being deprovisioned
  INACTIVE, // Tenant is inactive
}

// Interface for tenant address
export interface TenantAddress {
  id?: string;
  street?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
}

// Interface for tenant contact information
export interface TenantContact {
  id?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phoneNumber?: string;
  designation?: string;
}

// Interface for tenant details
export interface TenantDetails {
  id?: string;
  name?: string;
  key?: string;
  status?: TenantStatus;
  createdOn?: string;
  modifiedOn?: string;
  address?: TenantAddress;
}

export type TenantType = {
  contact?: TenantContact;
  tenantDetails: TenantDetails;
  id: string;
  name: string;
  status: TenantStatus;
  key: string;
  spocUserId?: string;
  domains: string[];
  leadId?: string;
  addressId: string;
  contacts?: ContactType[];
  resources?: ResourceType[];
  deleted: boolean;
  deletedOn: string;
  deletedBy: string;
  createdOn: string;
  modifiedOn: string;
  createdBy: string;
  modifiedBy: string;
  lang: string;
  subscriptions: Subscription[];
  files: IFile[];
};

/**
 * Interface representing the filter criteria for querying tenants.
 * It includes optional properties for limit, offset, order, status, date range, and search
 */
export interface TenantFilterDTO {
  limit?: number;
  offset?: number;
  order?: string;
  where?: {
    status?: {
      inq: number[];
    };
    createdOn?: {
      between?: string[];
    };
    name?: {
      ilike: string;
    };
  };
  include?: {relation: string}[];
}

/**
 * Interface representing the status of tenants.
 * It contains a record of tenant statuses where the key is the tenant ID and the value is
 */
export interface ITenantStatus {
  statuses: Record<string, string>;
}

/**
 * Interface representing the filter criteria for fetching tenants.
 * It includes optional properties for status, date range, and search value.
 */
export interface IApiTenantFilter {
  status?: number[];
  dateRange?: SelectedDateRange;
  include?: {relation: string}[];
  searchValue?: string;
  order?: string;
  limit?: number;
  offset?: number;
}

export type TenantApiDTO = IApiTenantFilter;
export type TenantApiForCountDTO = Omit<IApiTenantFilter, 'limit' | 'offset'>;
