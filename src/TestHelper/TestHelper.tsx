import {createTheme} from '@mui/material';
import {LocalizationProvider} from '@mui/x-date-pickers';
import {AdapterDateFns} from '@mui/x-date-pickers/AdapterDateFns';
import {configureStore} from '@reduxjs/toolkit';
import {render} from '@testing-library/react';
import {AnyObject} from 'Helpers/utils';
import {paletteConfig} from 'Providers/theme/default';
import ThemeProvider from 'Providers/theme/ThemeProvider';
import {Provider} from 'react-redux';
import {apiSlice} from 'redux/apiSlice';

const createTestStore = () => {
  return configureStore({
    reducer: {
      [apiSlice.reducerPath]: apiSlice.reducer,
    },
    middleware: getDefaultMiddleware => getDefaultMiddleware().concat(apiSlice.middleware),
  });
};

export const renderWithTheme = (ui: React.ReactElement) =>
  render(
    <Provider store={createTestStore()}>
      <ThemeProvider>
        <LocalizationProvider dateAdapter={AdapterDateFns}>{ui}</LocalizationProvider>
      </ThemeProvider>
    </Provider>,
  );

export const testingTheme = createTheme({
  palette: {
    ...paletteConfig.light,
  },
});
export const testingPalette = testingTheme.palette;

export function useThemeColor(path: string) {
  return path.split('.').reduce((obj: AnyObject, key) => obj?.[key], testingPalette);
}
