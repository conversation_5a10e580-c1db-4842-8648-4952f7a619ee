import {SxProps, Theme} from '@mui/material';

/**
 * Common styles for the header box.
 */
export const commonHeaderBoxStyle: SxProps = {
  pb: 1,
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
};

/**
 * Common styles for the left header.
 */
export const leftHeaderStyle: SxProps<Theme> = {
  fontWeight: 700,
  color: 'body.dark',
};

/**
 * Common styles for the action buttons.
 */

export const actionStyles: SxProps<Theme> = {
  color: 'body.500',
  fontSize: '1.25rem',
  fill: theme => theme.palette.white.main,
  cursor: 'pointer',
};
