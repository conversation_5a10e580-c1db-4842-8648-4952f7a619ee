# ---------- Build Stage ----------
FROM node:lts AS build
WORKDIR /app

# Copy package files and install all dependencies (incl. devDeps for build)
COPY package*.json ./
RUN npm install

# Copy rest of the source code
COPY . .

# Build the frontend app
RUN npm run build

# Generate the app config using configGenerator.js
# (ensure configGenerator.js reads from process.env)
RUN node configGenerator.js


# ---------- Production Stage ----------
FROM nginx:stable-alpine

# Copy built static files from build stage
COPY --from=build /app/build /usr/share/nginx/html

# Copy nginx configs
COPY --from=build /app/nginx/nginx.conf /etc/nginx/conf.d/default.conf
COPY --from=build /app/nginx/gzip.conf /etc/nginx/conf.d/gzip.conf

# Copy generated config.json
COPY --from=build /app/config.json /usr/share/nginx/html

EXPOSE 80

# Start Nginx
CMD ["nginx", "-g", "daemon off;"]
