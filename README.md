# repfabric-saas-control-plane

A React-based control plane for managing the SaaS infrastructure of Repfabric.  
This project provides a modern, scalable front-end interface for interacting with the control plane APIs.

---

## 🚀 Tech Stack

- **React** v19.1.0
- **Node.js** v20
- **npm** (Node Package Manager)
- **TypeScript** v5.8.3
- **Vite** v7.0.1
- **Vitest** v3.2.4

---

## 📦 Prerequisites

Ensure the following versions are installed:

- **Node.js**: v24.x.x
- **npm**: comes with Node 24 (you can verify with `npm -v`)

---

## 🔧 Getting Started

### 1. Clone the repository

```bash
git clone <your repo link>
cd repfabic-saas-control-plane
```

### 2. Install dependencies

```bash
npm install
```

### 3. Start the development server

```bash
npm run start
```
