# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
package-lock.json
.pnpm-store/

# Typescript
dist/
build/
*.tsbuildinfo

# Logs
logs/
*.log
*.log.*
lerna-debug.log

# Environment files
.env
.env.local
.env.*.local

# Editor / IDE
.vscode/
.idea/
*.swp
*.swo

# OS-specific
.DS_Store
Thumbs.db

# Docker
.dockerignore
docker-compose.override.yml

# Testing / Coverage
coverage/
.nyc_output/
.vitest/

# Storybook build
storybook-static/

# CDK / AWS
cdk.out/

# Local scripts & caches
*.tgz
.cache/
.tmp/
*.local

# Ignore generated config
config.json
config.local.json
