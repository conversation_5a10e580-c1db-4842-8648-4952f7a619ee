/* eslint-env node */
import fs from 'fs';
import path, { dirname } from 'path';
import { fileURLToPath } from 'url';

const currentDir = dirname(fileURLToPath(import.meta.url));
const outConfigFilePath = path.join(currentDir, 'config.json');

// Build config from environment variables (injected by Docker Compose/.env)
const config = {
  tenantApiBaseUrl: process.env.TENANT_API_BASE_URL,
  authApiBaseUrl: process.env.AUTH_API_BASE_URL,
  enableSessionTimeout: process.env.ENABLE_SESSION_TIMEOUT,
  expiryTimeInMinute: process.env.EXPIRY_TIME_IN_MINUTE,
  warningAlertTimeoutInMinute: process.env.WARNING_ALERT_TIMEOUT_IN_MINUTE,
};

try {
  fs.writeFileSync(outConfigFilePath, JSON.stringify(config, null, 2), 'utf-8');
  console.log(`Config file generated at ${outConfigFilePath}`);
} catch (error) {
  console.error(`Failed to write config.json: ${error.message}`);
  process.exit(1);
}
